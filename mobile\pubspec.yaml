name: code_rush
description: "Code Rush - Mobile arcade game with futuristic challenges"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.3
  bloc: ^8.1.2
  equatable: ^2.0.5

  # Networking
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.3.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1

  # Authentication
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  google_sign_in: ^6.1.6

  # UI Components
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_animate: ^4.2.0+1

  # Game Engine
  flame: ^1.12.0
  flame_audio: ^2.0.4
  audioplayers: ^5.2.1

  # Monetization
  google_mobile_ads: ^4.0.0

  # Device Features
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  connectivity_plus: ^5.0.2
  permission_handler: ^11.1.0

  # Utils
  intl: ^0.18.1
  uuid: ^4.2.1
  crypto: ^3.0.3
  url_launcher: ^6.2.1
  share_plus: ^7.2.1

  # Navigation
  go_router: ^12.1.3

  # Dependency Injection
  get_it: ^7.6.4
  injectable: ^2.3.2

  # Code Generation
  freezed_annotation: ^2.4.1
  json_serializable: ^6.7.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

  # Code Generation
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  injectable_generator: ^2.4.1
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.5
  mocktail: ^1.0.1
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/sounds/
    - assets/fonts/

  # Fonts
  fonts:
    - family: Orbitron
      fonts:
        - asset: assets/fonts/Orbitron-Regular.ttf
        - asset: assets/fonts/Orbitron-Bold.ttf
          weight: 700
        - asset: assets/fonts/Orbitron-Black.ttf
          weight: 900

    - family: RobotoMono
      fonts:
        - asset: assets/fonts/RobotoMono-Regular.ttf
        - asset: assets/fonts/RobotoMono-Bold.ttf
          weight: 700

    - family: Exo2
      fonts:
        - asset: assets/fonts/Exo2-Regular.ttf
        - asset: assets/fonts/Exo2-Bold.ttf
          weight: 700
        - asset: assets/fonts/Exo2-Light.ttf
          weight: 300

flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: lib/l10n
  output_dir: lib/generated
