import { z } from 'zod';

// Game Types
export enum GameType {
  MATCHING = 'matching',
  SEQUENCE = 'sequence',
  TIMING = 'timing',
  PUZZLE = 'puzzle'
}

// Game Difficulty Levels
export enum GameDifficulty {
  EASY = 1,
  MEDIUM = 3,
  HARD = 5,
  EXPERT = 7,
  MASTER = 10
}

// Game State
export enum GameState {
  WAITING = 'waiting',
  PLAYING = 'playing',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Base Game Configuration Schema
export const GameConfigSchema = z.object({
  type: z.nativeEnum(GameType),
  difficulty: z.number().min(1).max(10),
  timeLimit: z.number().optional(),
  maxAttempts: z.number().optional(),
  customParams: z.record(z.any()).optional(),
});

// Game Input Schema
export const GameInputSchema = z.object({
  type: z.string(),
  data: z.any(),
  timestamp: z.number(),
});

// Game Result Schema
export const GameResultSchema = z.object({
  score: z.number(),
  maxScore: z.number(),
  isCompleted: z.boolean(),
  isPerfect: z.boolean(),
  durationSeconds: z.number(),
  tokensEarned: z.number(),
  metadata: z.record(z.any()).optional(),
});

// Type definitions
export type GameConfig = z.infer<typeof GameConfigSchema>;
export type GameInput = z.infer<typeof GameInputSchema>;
export type GameResult = z.infer<typeof GameResultSchema>;

// Base Game Entity
export class Game {
  public readonly id: string;
  public readonly userId: string;
  public readonly config: GameConfig;
  public state: GameState;
  public score: number;
  public startTime: Date;
  public endTime?: Date;
  public metadata: Record<string, any>;

  constructor(
    id: string,
    userId: string,
    config: GameConfig,
    metadata: Record<string, any> = {}
  ) {
    this.id = id;
    this.userId = userId;
    this.config = config;
    this.state = GameState.WAITING;
    this.score = 0;
    this.startTime = new Date();
    this.metadata = metadata;
  }

  public start(): void {
    if (this.state !== GameState.WAITING) {
      throw new Error('Game can only be started from waiting state');
    }
    this.state = GameState.PLAYING;
    this.startTime = new Date();
  }

  public pause(): void {
    if (this.state !== GameState.PLAYING) {
      throw new Error('Game can only be paused while playing');
    }
    this.state = GameState.PAUSED;
  }

  public resume(): void {
    if (this.state !== GameState.PAUSED) {
      throw new Error('Game can only be resumed from paused state');
    }
    this.state = GameState.PLAYING;
  }

  public complete(result: GameResult): void {
    if (this.state !== GameState.PLAYING) {
      throw new Error('Game can only be completed while playing');
    }
    this.state = GameState.COMPLETED;
    this.score = result.score;
    this.endTime = new Date();
    this.metadata = { ...this.metadata, ...result.metadata };
  }

  public fail(): void {
    if (this.state !== GameState.PLAYING) {
      throw new Error('Game can only be failed while playing');
    }
    this.state = GameState.FAILED;
    this.endTime = new Date();
  }

  public getDuration(): number {
    const endTime = this.endTime || new Date();
    return Math.floor((endTime.getTime() - this.startTime.getTime()) / 1000);
  }

  public isTimeExpired(): boolean {
    if (!this.config.timeLimit) return false;
    return this.getDuration() >= this.config.timeLimit;
  }

  public getProgress(): number {
    // Override in specific game implementations
    return 0;
  }

  public toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      config: this.config,
      state: this.state,
      score: this.score,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.getDuration(),
      metadata: this.metadata,
    };
  }
}

// Game Engine Interface
export interface GameEngine {
  initialize(config: GameConfig): void;
  start(): void;
  processInput(input: GameInput): void;
  update(deltaTime: number): void;
  getState(): any;
  getResult(): GameResult;
  isCompleted(): boolean;
  isFailed(): boolean;
  cleanup(): void;
}

// Game Factory Interface
export interface GameFactory {
  createGame(type: GameType, config: GameConfig): GameEngine;
  getSupportedTypes(): GameType[];
  getDefaultConfig(type: GameType, difficulty: number): GameConfig;
}

// Scoring System
export class ScoringSystem {
  public static calculateBaseScore(
    gameType: GameType,
    difficulty: number,
    performance: number // 0-1 scale
  ): number {
    const baseScores = {
      [GameType.MATCHING]: 100,
      [GameType.SEQUENCE]: 150,
      [GameType.TIMING]: 200,
      [GameType.PUZZLE]: 250,
    };

    const base = baseScores[gameType];
    const difficultyMultiplier = 1 + (difficulty - 1) * 0.2;
    const performanceMultiplier = Math.max(0.1, performance);

    return Math.floor(base * difficultyMultiplier * performanceMultiplier);
  }

  public static calculateTimeBonus(
    timeLimit: number,
    actualTime: number,
    hasTimeBonus: boolean
  ): number {
    if (!hasTimeBonus || actualTime >= timeLimit) return 0;
    
    const timeRatio = (timeLimit - actualTime) / timeLimit;
    return Math.floor(timeRatio * 100); // Max 100 bonus points
  }

  public static calculatePerfectBonus(
    isPerfect: boolean,
    difficulty: number
  ): number {
    if (!isPerfect) return 0;
    return difficulty * 50; // 50 points per difficulty level
  }

  public static calculateTokenReward(
    score: number,
    maxScore: number,
    isPerfect: boolean,
    difficulty: number
  ): number {
    const baseTokens = 10;
    const scoreRatio = score / maxScore;
    const difficultyMultiplier = 1 + (difficulty - 1) * 0.1;
    const perfectMultiplier = isPerfect ? 2 : 1;

    return Math.floor(
      baseTokens * scoreRatio * difficultyMultiplier * perfectMultiplier
    );
  }
}

// Game Validation
export class GameValidator {
  public static validateConfig(config: GameConfig): boolean {
    try {
      GameConfigSchema.parse(config);
      return true;
    } catch {
      return false;
    }
  }

  public static validateInput(input: GameInput): boolean {
    try {
      GameInputSchema.parse(input);
      return true;
    } catch {
      return false;
    }
  }

  public static validateResult(result: GameResult): boolean {
    try {
      GameResultSchema.parse(result);
      return true;
    } catch {
      return false;
    }
  }

  public static validateGameDuration(
    startTime: Date,
    endTime: Date,
    minDuration: number
  ): boolean {
    const duration = (endTime.getTime() - startTime.getTime()) / 1000;
    return duration >= minDuration;
  }

  public static validateScore(
    score: number,
    maxScore: number,
    gameType: GameType
  ): boolean {
    if (score < 0 || score > maxScore) return false;
    
    // Additional validation based on game type
    switch (gameType) {
      case GameType.MATCHING:
        return score % 10 === 0; // Scores should be multiples of 10
      case GameType.SEQUENCE:
        return score % 5 === 0; // Scores should be multiples of 5
      default:
        return true;
    }
  }
}
