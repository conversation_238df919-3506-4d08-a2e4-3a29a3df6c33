import { GameEngine, GameFactory as IGameFactory, GameConfig, GameType } from '@/domain/entities/Game';
import { MatchingGameEngine } from './MatchingGameEngine';
import { SequenceGameEngine } from './SequenceGameEngine';
import { TimingGameEngine } from './TimingGameEngine';
import { PuzzleGameEngine } from './PuzzleGameEngine';

export class GameFactory implements IGameFactory {
  private engines: Map<GameType, new () => GameEngine> = new Map();

  constructor() {
    this.registerEngines();
  }

  public createGame(type: GameType, config: GameConfig): GameEngine {
    const EngineClass = this.engines.get(type);
    
    if (!EngineClass) {
      throw new Error(`Unsupported game type: ${type}`);
    }

    const engine = new EngineClass();
    engine.initialize(config);
    
    return engine;
  }

  public getSupportedTypes(): GameType[] {
    return Array.from(this.engines.keys());
  }

  public getDefaultConfig(type: GameType, difficulty: number): GameConfig {
    const baseConfig: GameConfig = {
      type,
      difficulty: Math.max(1, Math.min(10, difficulty)),
    };

    switch (type) {
      case GameType.MATCHING:
        return {
          ...baseConfig,
          timeLimit: this.calculateTimeLimit(difficulty, 60, 30),
          customParams: {
            pairCount: this.calculatePairCount(difficulty),
            allowMistakes: difficulty <= 5,
            showPreview: difficulty <= 3,
          },
        };

      case GameType.SEQUENCE:
        return {
          ...baseConfig,
          timeLimit: this.calculateTimeLimit(difficulty, 120, 60),
          customParams: {
            sequenceLength: this.calculateSequenceLength(difficulty),
            showDuration: Math.max(1, 4 - Math.floor(difficulty / 3)),
            allowMistakes: difficulty <= 6,
          },
        };

      case GameType.TIMING:
        return {
          ...baseConfig,
          timeLimit: this.calculateTimeLimit(difficulty, 30, 15),
          customParams: {
            targetCount: this.calculateTargetCount(difficulty),
            reactionWindow: this.calculateReactionWindow(difficulty),
            allowMisses: difficulty <= 4,
          },
        };

      case GameType.PUZZLE:
        return {
          ...baseConfig,
          timeLimit: this.calculateTimeLimit(difficulty, 180, 90),
          customParams: {
            gridSize: this.calculateGridSize(difficulty),
            moveLimit: this.calculateMoveLimit(difficulty),
            hintCount: Math.max(0, 3 - Math.floor(difficulty / 3)),
          },
        };

      default:
        return baseConfig;
    }
  }

  public getGameInfo(type: GameType): GameInfo {
    const gameInfoMap: Record<GameType, GameInfo> = {
      [GameType.MATCHING]: {
        name: 'Matching Game',
        description: 'Connect related elements to form pairs',
        icon: 'link',
        category: 'Logic',
        estimatedDuration: '1-2 minutes',
        skills: ['Pattern Recognition', 'Memory', 'Speed'],
        difficultyRange: [1, 10],
        features: ['Time Bonus', 'Perfect Score', 'Multiple Categories'],
      },
      [GameType.SEQUENCE]: {
        name: 'Sequence Game',
        description: 'Complete logical patterns and sequences',
        icon: 'trending_up',
        category: 'Logic',
        estimatedDuration: '2-3 minutes',
        skills: ['Logic', 'Pattern Recognition', 'Memory'],
        difficultyRange: [1, 10],
        features: ['Multiple Pattern Types', 'Progressive Difficulty', 'Time Pressure'],
      },
      [GameType.TIMING]: {
        name: 'Timing Game',
        description: 'Test your reflexes and precision timing',
        icon: 'timer',
        category: 'Reflex',
        estimatedDuration: '30-60 seconds',
        skills: ['Reflexes', 'Precision', 'Focus'],
        difficultyRange: [1, 10],
        features: ['Reaction Time', 'Combo System', 'Precision Scoring'],
      },
      [GameType.PUZZLE]: {
        name: 'Puzzle Game',
        description: 'Solve complex puzzles with strategy',
        icon: 'extension',
        category: 'Strategy',
        estimatedDuration: '3-5 minutes',
        skills: ['Strategy', 'Problem Solving', 'Planning'],
        difficultyRange: [1, 10],
        features: ['Multiple Solutions', 'Hint System', 'Move Optimization'],
      },
    };

    return gameInfoMap[type];
  }

  public validateGameConfig(config: GameConfig): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!Object.values(GameType).includes(config.type)) {
      errors.push(`Invalid game type: ${config.type}`);
    }

    if (config.difficulty < 1 || config.difficulty > 10) {
      errors.push('Difficulty must be between 1 and 10');
    }

    if (config.timeLimit && config.timeLimit < 10) {
      warnings.push('Time limit is very short, consider increasing it');
    }

    // Type-specific validation
    switch (config.type) {
      case GameType.MATCHING:
        this.validateMatchingConfig(config, errors, warnings);
        break;
      case GameType.SEQUENCE:
        this.validateSequenceConfig(config, errors, warnings);
        break;
      case GameType.TIMING:
        this.validateTimingConfig(config, errors, warnings);
        break;
      case GameType.PUZZLE:
        this.validatePuzzleConfig(config, errors, warnings);
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private registerEngines(): void {
    this.engines.set(GameType.MATCHING, MatchingGameEngine);
    this.engines.set(GameType.SEQUENCE, SequenceGameEngine);
    this.engines.set(GameType.TIMING, TimingGameEngine);
    this.engines.set(GameType.PUZZLE, PuzzleGameEngine);
  }

  private calculateTimeLimit(difficulty: number, maxTime: number, minTime: number): number {
    const ratio = (difficulty - 1) / 9; // 0 to 1
    return Math.floor(maxTime - (maxTime - minTime) * ratio);
  }

  private calculatePairCount(difficulty: number): number {
    return Math.min(12, 4 + Math.floor((difficulty - 1) / 2) * 2);
  }

  private calculateSequenceLength(difficulty: number): number {
    return Math.min(12, 4 + Math.floor((difficulty - 1) / 2) * 2);
  }

  private calculateTargetCount(difficulty: number): number {
    return Math.min(20, 5 + Math.floor((difficulty - 1) / 2) * 3);
  }

  private calculateReactionWindow(difficulty: number): number {
    // Reaction window in milliseconds
    return Math.max(200, 1000 - (difficulty - 1) * 80);
  }

  private calculateGridSize(difficulty: number): number {
    return Math.min(6, 3 + Math.floor((difficulty - 1) / 3));
  }

  private calculateMoveLimit(difficulty: number): number {
    const gridSize = this.calculateGridSize(difficulty);
    const optimalMoves = gridSize * gridSize;
    return Math.floor(optimalMoves * (1.5 - (difficulty - 1) * 0.05));
  }

  private validateMatchingConfig(config: GameConfig, errors: string[], warnings: string[]): void {
    const params = config.customParams;
    if (!params) return;

    if (params.pairCount && (params.pairCount < 2 || params.pairCount > 15)) {
      errors.push('Pair count must be between 2 and 15');
    }

    if (params.pairCount && params.pairCount > 10) {
      warnings.push('High pair count may be too challenging');
    }
  }

  private validateSequenceConfig(config: GameConfig, errors: string[], warnings: string[]): void {
    const params = config.customParams;
    if (!params) return;

    if (params.sequenceLength && (params.sequenceLength < 3 || params.sequenceLength > 15)) {
      errors.push('Sequence length must be between 3 and 15');
    }

    if (params.showDuration && params.showDuration < 0.5) {
      warnings.push('Show duration is very short');
    }
  }

  private validateTimingConfig(config: GameConfig, errors: string[], warnings: string[]): void {
    const params = config.customParams;
    if (!params) return;

    if (params.targetCount && (params.targetCount < 3 || params.targetCount > 25)) {
      errors.push('Target count must be between 3 and 25');
    }

    if (params.reactionWindow && params.reactionWindow < 100) {
      warnings.push('Reaction window is extremely short');
    }
  }

  private validatePuzzleConfig(config: GameConfig, errors: string[], warnings: string[]): void {
    const params = config.customParams;
    if (!params) return;

    if (params.gridSize && (params.gridSize < 3 || params.gridSize > 8)) {
      errors.push('Grid size must be between 3 and 8');
    }

    if (params.moveLimit && params.moveLimit < 5) {
      warnings.push('Move limit is very restrictive');
    }
  }
}

// Type definitions
export interface GameInfo {
  name: string;
  description: string;
  icon: string;
  category: string;
  estimatedDuration: string;
  skills: string[];
  difficultyRange: [number, number];
  features: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Singleton instance
export const gameFactory = new GameFactory();
