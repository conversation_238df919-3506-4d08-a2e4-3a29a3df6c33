import winston from 'winston';
import { config } from './config';

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} ${level}: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: {
    service: 'code-rush-api',
    environment: config.env
  },
  transports: [
    // File transport for errors
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // File transport for all logs
    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

// Add console transport for non-production environments
if (!config.isProduction) {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// Create logs directory if it doesn't exist
import fs from 'fs';
import path from 'path';

const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Enhanced logging methods with context
export const createLogger = (context: string) => {
  return {
    error: (message: string, meta?: any) => {
      logger.error(message, { context, ...meta });
    },
    warn: (message: string, meta?: any) => {
      logger.warn(message, { context, ...meta });
    },
    info: (message: string, meta?: any) => {
      logger.info(message, { context, ...meta });
    },
    debug: (message: string, meta?: any) => {
      logger.debug(message, { context, ...meta });
    },
  };
};

// Game-specific logging methods
export const gameLogger = {
  sessionStart: (userId: string, gameType: string, sessionId: string) => {
    logger.info('Game session started', {
      event: 'game_session_start',
      userId,
      gameType,
      sessionId,
      timestamp: new Date().toISOString()
    });
  },
  
  sessionEnd: (userId: string, gameType: string, sessionId: string, score: number, duration: number) => {
    logger.info('Game session completed', {
      event: 'game_session_end',
      userId,
      gameType,
      sessionId,
      score,
      duration,
      timestamp: new Date().toISOString()
    });
  },
  
  tokenEarned: (userId: string, amount: number, source: string, referenceId?: string) => {
    logger.info('Tokens earned', {
      event: 'tokens_earned',
      userId,
      amount,
      source,
      referenceId,
      timestamp: new Date().toISOString()
    });
  },
  
  fraudDetected: (userId: string, type: string, severity: string, evidence: any) => {
    logger.warn('Fraud detected', {
      event: 'fraud_detected',
      userId,
      type,
      severity,
      evidence,
      timestamp: new Date().toISOString()
    });
  },
  
  adViewed: (userId: string, adType: string, tokensEarned: number) => {
    logger.info('Ad viewed', {
      event: 'ad_viewed',
      userId,
      adType,
      tokensEarned,
      timestamp: new Date().toISOString()
    });
  },
  
  achievementUnlocked: (userId: string, achievementId: string, achievementName: string) => {
    logger.info('Achievement unlocked', {
      event: 'achievement_unlocked',
      userId,
      achievementId,
      achievementName,
      timestamp: new Date().toISOString()
    });
  }
};

// Security logging methods
export const securityLogger = {
  loginAttempt: (email: string, success: boolean, ip: string, userAgent: string) => {
    const level = success ? 'info' : 'warn';
    logger[level]('Login attempt', {
      event: 'login_attempt',
      email,
      success,
      ip,
      userAgent,
      timestamp: new Date().toISOString()
    });
  },
  
  rateLimitExceeded: (ip: string, endpoint: string, userAgent: string) => {
    logger.warn('Rate limit exceeded', {
      event: 'rate_limit_exceeded',
      ip,
      endpoint,
      userAgent,
      timestamp: new Date().toISOString()
    });
  },
  
  suspiciousActivity: (userId: string, activity: string, details: any) => {
    logger.warn('Suspicious activity detected', {
      event: 'suspicious_activity',
      userId,
      activity,
      details,
      timestamp: new Date().toISOString()
    });
  },
  
  dataAccess: (userId: string, resource: string, action: string) => {
    logger.info('Data access', {
      event: 'data_access',
      userId,
      resource,
      action,
      timestamp: new Date().toISOString()
    });
  }
};

// Performance logging methods
export const performanceLogger = {
  apiRequest: (method: string, endpoint: string, duration: number, statusCode: number, userId?: string) => {
    logger.info('API request', {
      event: 'api_request',
      method,
      endpoint,
      duration,
      statusCode,
      userId,
      timestamp: new Date().toISOString()
    });
  },
  
  databaseQuery: (query: string, duration: number, success: boolean) => {
    logger.debug('Database query', {
      event: 'database_query',
      query,
      duration,
      success,
      timestamp: new Date().toISOString()
    });
  },
  
  cacheOperation: (operation: string, key: string, hit: boolean, duration: number) => {
    logger.debug('Cache operation', {
      event: 'cache_operation',
      operation,
      key,
      hit,
      duration,
      timestamp: new Date().toISOString()
    });
  }
};

export { logger };
export default logger;
