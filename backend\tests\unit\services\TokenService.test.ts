import { TokenService, TokenServiceDependencies } from '@/application/services/TokenService';
import { 
  TokenTransactionEntity, 
  TokenBalanceEntity, 
  TokenTransactionType, 
  TokenSource 
} from '@/domain/entities/Token';

// Mock dependencies
const mockTokenRepository = {
  createTransaction: jest.fn(),
  getBalance: jest.fn(),
  updateBalance: jest.fn(),
  getTransactionHistory: jest.fn(),
  getDailyEarnings: jest.fn(),
  getWeeklyEarnings: jest.fn(),
  getMonthlyEarnings: jest.fn(),
};

const mockUserRepository = {
  getUserById: jest.fn(),
  updateUserLevel: jest.fn(),
};

const mockFraudDetectionService = {
  validateTokenTransaction: jest.fn(),
  checkDailyLimits: jest.fn(),
  reportSuspiciousActivity: jest.fn(),
};

const mockNotificationService = {
  sendTokenEarnedNotification: jest.fn(),
  sendLevelUpNotification: jest.fn(),
  sendDailyLimitNotification: jest.fn(),
};

const dependencies: TokenServiceDependencies = {
  tokenRepository: mockTokenRepository,
  userRepository: mockUserRepository,
  fraudDetectionService: mockFraudDetectionService,
  notificationService: mockNotificationService,
};

describe('TokenService', () => {
  let tokenService: TokenService;

  beforeEach(() => {
    tokenService = new TokenService(dependencies);
    jest.clearAllMocks();
  });

  describe('awardGameTokens', () => {
    const userId = 'user-123';
    const gameType = 'matching';
    const difficulty = 5;
    const score = 800;
    const maxScore = 1000;
    const isPerfect = false;
    const duration = 120;
    const gameSessionId = 'session-123';

    beforeEach(() => {
      mockUserRepository.getUserById.mockResolvedValue({
        id: userId,
        level: 5,
        tier: 'silver',
        isActive: true,
        createdAt: new Date(),
      });

      mockFraudDetectionService.validateTokenTransaction.mockResolvedValue(true);
      
      mockTokenRepository.getBalance.mockResolvedValue(
        new TokenBalanceEntity({
          userId,
          totalTokens: 500,
          availableTokens: 500,
          pendingTokens: 0,
          lifetimeEarned: 500,
          lifetimeSpent: 0,
        })
      );

      mockTokenRepository.createTransaction.mockImplementation((transaction) => 
        Promise.resolve(transaction)
      );

      mockTokenRepository.updateBalance.mockImplementation((balance) => 
        Promise.resolve(balance)
      );
    });

    it('should award tokens for game completion', async () => {
      const result = await tokenService.awardGameTokens(
        userId,
        gameType,
        difficulty,
        score,
        maxScore,
        isPerfect,
        duration,
        gameSessionId
      );

      expect(result).toBeInstanceOf(TokenTransactionEntity);
      expect(result.userId).toBe(userId);
      expect(result.type).toBe(TokenTransactionType.EARNED);
      expect(result.source).toBe(TokenSource.GAME_COMPLETION);
      expect(result.amount).toBeGreaterThan(0);
      expect(result.referenceId).toBe(gameSessionId);

      expect(mockTokenRepository.createTransaction).toHaveBeenCalledWith(
        expect.any(TokenTransactionEntity)
      );
      expect(mockTokenRepository.updateBalance).toHaveBeenCalled();
      expect(mockNotificationService.sendTokenEarnedNotification).toHaveBeenCalledWith(
        userId,
        result.amount,
        TokenSource.GAME_COMPLETION
      );
    });

    it('should throw error for inactive user', async () => {
      mockUserRepository.getUserById.mockResolvedValue({
        id: userId,
        level: 5,
        tier: 'silver',
        isActive: false,
        createdAt: new Date(),
      });

      await expect(
        tokenService.awardGameTokens(
          userId,
          gameType,
          difficulty,
          score,
          maxScore,
          isPerfect,
          duration,
          gameSessionId
        )
      ).rejects.toThrow('User not found or inactive');
    });

    it('should throw error when fraud validation fails', async () => {
      mockFraudDetectionService.validateTokenTransaction.mockResolvedValue(false);

      await expect(
        tokenService.awardGameTokens(
          userId,
          gameType,
          difficulty,
          score,
          maxScore,
          isPerfect,
          duration,
          gameSessionId
        )
      ).rejects.toThrow('Transaction failed security validation');
    });

    it('should calculate higher rewards for perfect games', async () => {
      const perfectResult = await tokenService.awardGameTokens(
        userId,
        gameType,
        difficulty,
        maxScore, // Perfect score
        maxScore,
        true, // Perfect game
        duration,
        gameSessionId
      );

      const imperfectResult = await tokenService.awardGameTokens(
        userId,
        gameType,
        difficulty,
        score, // Lower score
        maxScore,
        false, // Not perfect
        duration,
        'session-456'
      );

      expect(perfectResult.amount).toBeGreaterThan(imperfectResult.amount);
    });
  });

  describe('awardAdTokens', () => {
    const userId = 'user-123';
    const adType = 'rewarded';
    const adProvider = 'admob';
    const adId = 'ad-123';

    beforeEach(() => {
      mockFraudDetectionService.checkDailyLimits.mockResolvedValue(true);
      
      mockUserRepository.getUserById.mockResolvedValue({
        id: userId,
        level: 3,
        tier: 'bronze',
        isActive: true,
        createdAt: new Date(),
      });

      mockTokenRepository.getBalance.mockResolvedValue(
        new TokenBalanceEntity({
          userId,
          totalTokens: 100,
          availableTokens: 100,
          pendingTokens: 0,
          lifetimeEarned: 100,
          lifetimeSpent: 0,
        })
      );

      mockTokenRepository.createTransaction.mockImplementation((transaction) => 
        Promise.resolve(transaction)
      );

      mockTokenRepository.updateBalance.mockImplementation((balance) => 
        Promise.resolve(balance)
      );
    });

    it('should award tokens for ad viewing', async () => {
      const result = await tokenService.awardAdTokens(userId, adType, adProvider, adId);

      expect(result).toBeInstanceOf(TokenTransactionEntity);
      expect(result.userId).toBe(userId);
      expect(result.type).toBe(TokenTransactionType.EARNED);
      expect(result.source).toBe(TokenSource.AD_VIEW);
      expect(result.referenceId).toBe(adId);
      expect(result.metadata).toEqual({
        adType,
        adProvider,
        userLevel: 3,
      });
    });

    it('should throw error when daily limit is reached', async () => {
      mockFraudDetectionService.checkDailyLimits.mockResolvedValue(false);

      await expect(
        tokenService.awardAdTokens(userId, adType, adProvider, adId)
      ).rejects.toThrow('Daily ad viewing limit reached');

      expect(mockNotificationService.sendDailyLimitNotification).toHaveBeenCalledWith(
        userId,
        'ad_viewing'
      );
    });
  });

  describe('spendTokens', () => {
    const userId = 'user-123';
    const amount = 100;
    const category = 'power_ups' as any;
    const itemId = 'item-123';

    beforeEach(() => {
      mockTokenRepository.getBalance.mockResolvedValue(
        new TokenBalanceEntity({
          userId,
          totalTokens: 500,
          availableTokens: 500,
          pendingTokens: 0,
          lifetimeEarned: 500,
          lifetimeSpent: 0,
        })
      );

      mockTokenRepository.createTransaction.mockImplementation((transaction) => 
        Promise.resolve(transaction)
      );

      mockTokenRepository.updateBalance.mockImplementation((balance) => 
        Promise.resolve(balance)
      );
    });

    it('should spend tokens successfully', async () => {
      const result = await tokenService.spendTokens(userId, amount, category, itemId);

      expect(result).toBeInstanceOf(TokenTransactionEntity);
      expect(result.userId).toBe(userId);
      expect(result.type).toBe(TokenTransactionType.SPENT);
      expect(result.amount).toBe(amount);
      expect(result.category).toBe(category);
      expect(result.referenceId).toBe(itemId);
    });

    it('should throw error for insufficient tokens', async () => {
      mockTokenRepository.getBalance.mockResolvedValue(
        new TokenBalanceEntity({
          userId,
          totalTokens: 50,
          availableTokens: 50,
          pendingTokens: 0,
          lifetimeEarned: 50,
          lifetimeSpent: 0,
        })
      );

      await expect(
        tokenService.spendTokens(userId, amount, category, itemId)
      ).rejects.toThrow('Insufficient tokens');
    });
  });

  describe('getBalance', () => {
    const userId = 'user-123';

    it('should return existing balance', async () => {
      const existingBalance = new TokenBalanceEntity({
        userId,
        totalTokens: 1000,
        availableTokens: 800,
        pendingTokens: 200,
        lifetimeEarned: 1500,
        lifetimeSpent: 500,
      });

      mockTokenRepository.getBalance.mockResolvedValue(existingBalance);

      const result = await tokenService.getBalance(userId);

      expect(result).toBe(existingBalance);
      expect(mockTokenRepository.getBalance).toHaveBeenCalledWith(userId);
    });

    it('should create initial balance for new user', async () => {
      mockTokenRepository.getBalance.mockResolvedValue(null);
      
      const newBalance = new TokenBalanceEntity({
        userId,
        totalTokens: 0,
        availableTokens: 0,
        pendingTokens: 0,
        lifetimeEarned: 0,
        lifetimeSpent: 0,
      });

      mockTokenRepository.updateBalance.mockResolvedValue(newBalance);

      const result = await tokenService.getBalance(userId);

      expect(result.userId).toBe(userId);
      expect(result.totalTokens).toBe(0);
      expect(mockTokenRepository.updateBalance).toHaveBeenCalledWith(
        expect.any(TokenBalanceEntity)
      );
    });
  });

  describe('getEarningsSummary', () => {
    const userId = 'user-123';

    it('should return earnings summary', async () => {
      mockTokenRepository.getDailyEarnings.mockResolvedValue(50);
      mockTokenRepository.getWeeklyEarnings.mockResolvedValue(300);
      mockTokenRepository.getMonthlyEarnings.mockResolvedValue(1200);

      const result = await tokenService.getEarningsSummary(userId);

      expect(result).toEqual({
        daily: 50,
        weekly: 300,
        monthly: 1200,
        dailyLimit: expect.any(Number),
        remainingDaily: expect.any(Number),
      });

      expect(result.remainingDaily).toBeGreaterThanOrEqual(0);
    });
  });
});
