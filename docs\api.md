# API Documentation - Code Rush

## 📋 Información General

- **Base URL**: `https://api.coderush.com/v1`
- **Autenticación**: JWT <PERSON>
- **Formato**: JSON
- **Versionado**: URL path (`/v1/`)

## 🔐 Autenticación

### POST /auth/login
Iniciar sesión con email y contraseña.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "player123",
      "level": 5,
      "totalTokens": 1250
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token",
      "expiresIn": 604800
    }
  }
}
```

### POST /auth/register
Registrar nuevo usuario.

**Request:**
```json
{
  "email": "<EMAIL>",
  "username": "player123",
  "password": "password123",
  "confirmPassword": "password123"
}
```

### POST /auth/refresh
Renovar token de acceso.

**Request:**
```json
{
  "refreshToken": "refresh_token"
}
```

### POST /auth/logout
Cerrar sesión (invalidar tokens).

## 🎮 Juegos

### GET /games/types
Obtener tipos de juegos disponibles.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "type": "matching",
      "name": "Matching Game",
      "description": "Connect related elements",
      "icon": "link",
      "category": "Logic",
      "difficultyRange": [1, 10],
      "estimatedDuration": "1-2 minutes"
    }
  ]
}
```

### POST /games/start
Iniciar nueva sesión de juego.

**Request:**
```json
{
  "gameType": "matching",
  "difficulty": 5
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "session_uuid",
    "gameType": "matching",
    "difficulty": 5,
    "config": {
      "timeLimit": 45,
      "pairCount": 8
    },
    "startedAt": "2024-01-15T10:30:00Z"
  }
}
```

### POST /games/{sessionId}/input
Enviar entrada del usuario al juego.

**Request:**
```json
{
  "type": "select_item",
  "data": {
    "itemId": "item_123"
  },
  "timestamp": 1705312200000
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "gameState": {
      "score": 150,
      "selectedItems": ["item_123"],
      "matchedPairs": 2,
      "timeRemaining": 35
    },
    "isCompleted": false,
    "isFailed": false
  }
}
```

### POST /games/{sessionId}/end
Finalizar sesión de juego.

**Response:**
```json
{
  "success": true,
  "data": {
    "result": {
      "score": 850,
      "maxScore": 1000,
      "isCompleted": true,
      "isPerfect": false,
      "durationSeconds": 42,
      "tokensEarned": 35
    },
    "transaction": {
      "id": "tx_uuid",
      "amount": 35,
      "type": "earned"
    }
  }
}
```

### GET /games/history
Obtener historial de juegos del usuario.

**Query Parameters:**
- `limit` (optional): Número de resultados (default: 20, max: 100)
- `offset` (optional): Offset para paginación (default: 0)
- `gameType` (optional): Filtrar por tipo de juego

**Response:**
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "id": "session_uuid",
        "gameType": "matching",
        "difficulty": 5,
        "score": 850,
        "isCompleted": true,
        "tokensEarned": 35,
        "completedAt": "2024-01-15T10:32:15Z"
      }
    ],
    "pagination": {
      "total": 150,
      "limit": 20,
      "offset": 0,
      "hasMore": true
    }
  }
}
```

## 🪙 Tokens (IB Tokens)

### GET /tokens/balance
Obtener balance actual de tokens.

**Response:**
```json
{
  "success": true,
  "data": {
    "totalTokens": 1250,
    "availableTokens": 1100,
    "pendingTokens": 150,
    "lifetimeEarned": 5000,
    "lifetimeSpent": 3750
  }
}
```

### GET /tokens/transactions
Obtener historial de transacciones.

**Query Parameters:**
- `limit` (optional): Número de resultados (default: 50)
- `offset` (optional): Offset para paginación
- `type` (optional): Filtrar por tipo (earned, spent, withdrawn)
- `source` (optional): Filtrar por fuente (game, ad, survey)

**Response:**
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "tx_uuid",
        "type": "earned",
        "amount": 35,
        "source": "game",
        "description": "Game completion: matching (Level 5)",
        "createdAt": "2024-01-15T10:32:15Z"
      }
    ],
    "pagination": {
      "total": 200,
      "limit": 50,
      "offset": 0
    }
  }
}
```

### GET /tokens/earnings
Obtener resumen de ganancias.

**Response:**
```json
{
  "success": true,
  "data": {
    "daily": 150,
    "weekly": 800,
    "monthly": 3200,
    "dailyLimit": 1000,
    "remainingDaily": 850
  }
}
```

### POST /tokens/spend
Gastar tokens en items.

**Request:**
```json
{
  "amount": 100,
  "category": "power_ups",
  "itemId": "double_score_boost",
  "description": "Double Score Boost"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "transaction": {
      "id": "tx_uuid",
      "amount": 100,
      "type": "spent",
      "category": "power_ups"
    },
    "newBalance": 1000
  }
}
```

## 📺 Anuncios

### POST /ads/view
Registrar visualización de anuncio.

**Request:**
```json
{
  "adType": "rewarded",
  "adProvider": "admob",
  "adId": "ad_unit_123",
  "duration": 30,
  "completed": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tokensEarned": 5,
    "transaction": {
      "id": "tx_uuid",
      "amount": 5,
      "type": "earned",
      "source": "ad"
    },
    "dailyAdCount": 15,
    "dailyLimit": 50
  }
}
```

### GET /ads/status
Obtener estado de anuncios del usuario.

**Response:**
```json
{
  "success": true,
  "data": {
    "dailyViews": 15,
    "dailyLimit": 50,
    "canViewAd": true,
    "nextAdAvailableAt": null,
    "availableAdTypes": ["interstitial", "rewarded"]
  }
}
```

## 🏆 Logros y Misiones

### GET /achievements
Obtener logros del usuario.

**Response:**
```json
{
  "success": true,
  "data": {
    "unlocked": [
      {
        "id": "first_game",
        "name": "First Steps",
        "description": "Complete your first game",
        "tier": "bronze",
        "tokensEarned": 50,
        "unlockedAt": "2024-01-15T09:00:00Z"
      }
    ],
    "available": [
      {
        "id": "perfect_streak",
        "name": "Perfect Streak",
        "description": "Complete 5 perfect games in a row",
        "tier": "gold",
        "reward": 200,
        "progress": {
          "current": 2,
          "target": 5
        }
      }
    ]
  }
}
```

### GET /missions/daily
Obtener misiones diarias.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "mission_uuid",
      "type": "play_games",
      "description": "Play 5 games",
      "targetValue": 5,
      "currentProgress": 2,
      "rewardTokens": 100,
      "expiresAt": "2024-01-16T00:00:00Z",
      "isCompleted": false
    }
  ]
}
```

## 📊 Clasificaciones

### GET /leaderboard
Obtener clasificaciones.

**Query Parameters:**
- `gameType` (optional): Tipo de juego
- `period` (optional): Período (daily, weekly, monthly, all_time)
- `limit` (optional): Número de resultados (default: 100)

**Response:**
```json
{
  "success": true,
  "data": {
    "leaderboard": [
      {
        "rank": 1,
        "userId": "user_uuid",
        "username": "player123",
        "score": 15000,
        "gamesPlayed": 50
      }
    ],
    "userRank": {
      "rank": 25,
      "score": 8500,
      "gamesPlayed": 30
    },
    "period": "weekly",
    "gameType": "matching"
  }
}
```

## 👤 Perfil de Usuario

### GET /profile
Obtener perfil del usuario.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_uuid",
    "username": "player123",
    "email": "<EMAIL>",
    "level": 15,
    "experience": 12500,
    "totalTokens": 2500,
    "gamesPlayed": 150,
    "totalScore": 125000,
    "achievements": 12,
    "joinedAt": "2024-01-01T00:00:00Z"
  }
}
```

### PUT /profile
Actualizar perfil del usuario.

**Request:**
```json
{
  "username": "newusername",
  "displayName": "New Display Name",
  "avatarUrl": "https://example.com/avatar.jpg"
}
```

## 📈 Estadísticas

### GET /stats/overview
Obtener resumen de estadísticas.

**Response:**
```json
{
  "success": true,
  "data": {
    "gamesPlayed": 150,
    "totalScore": 125000,
    "averageScore": 833,
    "perfectGames": 25,
    "favoriteGameType": "matching",
    "totalPlayTime": 7200,
    "tokensEarned": 5000,
    "currentStreak": 5,
    "bestStreak": 12
  }
}
```

## ⚠️ Códigos de Error

### Códigos HTTP Estándar
- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

### Códigos de Error Personalizados

```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_TOKENS",
    "message": "Not enough tokens to complete this action",
    "details": {
      "required": 100,
      "available": 50
    }
  }
}
```

**Códigos Comunes:**
- `INVALID_CREDENTIALS` - Credenciales inválidas
- `USER_NOT_FOUND` - Usuario no encontrado
- `GAME_SESSION_NOT_FOUND` - Sesión de juego no encontrada
- `INSUFFICIENT_TOKENS` - Tokens insuficientes
- `DAILY_LIMIT_REACHED` - Límite diario alcanzado
- `FRAUD_DETECTED` - Actividad fraudulenta detectada
- `VALIDATION_ERROR` - Error de validación
- `RATE_LIMIT_EXCEEDED` - Límite de velocidad excedido

## 🔒 Rate Limiting

- **Autenticación**: 5 intentos por minuto por IP
- **Juegos**: 30 sesiones por hora por usuario
- **Tokens**: 100 transacciones por hora por usuario
- **Anuncios**: 60 visualizaciones por hora por usuario
- **API General**: 1000 requests por hora por usuario

## 📝 Notas Adicionales

### Paginación
Todas las respuestas paginadas incluyen:
```json
{
  "pagination": {
    "total": 500,
    "limit": 20,
    "offset": 0,
    "hasMore": true
  }
}
```

### Timestamps
Todos los timestamps están en formato ISO 8601 UTC.

### Versionado
La API usa versionado en la URL. La versión actual es `v1`.

### WebSockets
Para actualizaciones en tiempo real:
- **URL**: `wss://api.coderush.com/ws`
- **Autenticación**: Query parameter `token=jwt_token`
- **Eventos**: `game_update`, `token_earned`, `achievement_unlocked`
