import { TokenTransactionEntity, TokenSource } from '@/domain/entities/Token';
import { GameSession } from '@/domain/entities/GameSession';
import { createLogger, securityLogger } from '@/shared/logger';
import { config } from '@/shared/config';

const logger = createLogger('FraudDetectionService');

export enum FraudSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum FraudType {
  SUSPICIOUS_PATTERN = 'suspicious_pattern',
  IMPOSSIBLE_SCORE = 'impossible_score',
  BOT_BEHAVIOR = 'bot_behavior',
  RAPID_GAMEPLAY = 'rapid_gameplay',
  DEVICE_MANIPULATION = 'device_manipulation',
  TIME_MANIPULATION = 'time_manipulation',
  EXCESSIVE_EARNINGS = 'excessive_earnings',
  DUPLICATE_ACTIONS = 'duplicate_actions'
}

export interface FraudReport {
  id: string;
  userId: string;
  type: FraudType;
  severity: FraudSeverity;
  description: string;
  evidence: Record<string, any>;
  status: 'pending' | 'investigating' | 'resolved' | 'false_positive';
  actionTaken?: string;
  createdAt: Date;
  resolvedAt?: Date;
}

export interface UserBehaviorMetrics {
  userId: string;
  sessionCount: number;
  averageSessionDuration: number;
  gamesPerSession: number;
  averageScore: number;
  perfectGameRatio: number;
  timesBetweenSessions: number[];
  deviceFingerprints: string[];
  ipAddresses: string[];
  userAgents: string[];
  touchPatterns: TouchMetrics[];
  lastActivity: Date;
}

export interface TouchMetrics {
  timestamp: number;
  x: number;
  y: number;
  pressure: number;
  duration: number;
  velocity: number;
}

export interface DeviceFingerprint {
  userId: string;
  fingerprint: string;
  deviceInfo: {
    platform: string;
    model: string;
    osVersion: string;
    screenResolution: string;
    timezone: string;
    language: string;
  };
  firstSeen: Date;
  lastSeen: Date;
  isActive: boolean;
}

export interface FraudDetectionDependencies {
  fraudRepository: FraudRepository;
  userRepository: UserRepository;
  gameRepository: GameRepository;
  cacheService: CacheService;
  notificationService: NotificationService;
}

export interface FraudRepository {
  createReport(report: FraudReport): Promise<FraudReport>;
  getUserBehaviorMetrics(userId: string): Promise<UserBehaviorMetrics | null>;
  updateUserBehaviorMetrics(metrics: UserBehaviorMetrics): Promise<void>;
  getDeviceFingerprint(userId: string, fingerprint: string): Promise<DeviceFingerprint | null>;
  saveDeviceFingerprint(fingerprint: DeviceFingerprint): Promise<void>;
  getDailyEarnings(userId: string, date: Date): Promise<number>;
  getHourlyGameCount(userId: string, hour: Date): Promise<number>;
  getRecentTransactions(userId: string, minutes: number): Promise<TokenTransactionEntity[]>;
}

export interface UserRepository {
  getUserById(id: string): Promise<User | null>;
  suspendUser(userId: string, reason: string, duration?: number): Promise<void>;
  flagUser(userId: string, flag: string): Promise<void>;
}

export interface GameRepository {
  getRecentSessions(userId: string, limit: number): Promise<GameSession[]>;
  getSessionById(sessionId: string): Promise<GameSession | null>;
}

export interface CacheService {
  get(key: string): Promise<any>;
  set(key: string, value: any, ttl?: number): Promise<void>;
  increment(key: string, amount?: number): Promise<number>;
}

export interface NotificationService {
  notifyAdmins(message: string, data: any): Promise<void>;
  sendUserWarning(userId: string, reason: string): Promise<void>;
}

export interface User {
  id: string;
  email: string;
  createdAt: Date;
  isActive: boolean;
  isSuspended: boolean;
  flags: string[];
}

export class FraudDetectionService {
  constructor(private dependencies: FraudDetectionDependencies) {}

  // Main validation method for token transactions
  public async validateTokenTransaction(transaction: TokenTransactionEntity): Promise<boolean> {
    if (!config.fraud.enabled) return true;

    try {
      const checks = await Promise.all([
        this.checkDailyLimits(transaction.userId, transaction.source),
        this.checkRapidTransactions(transaction),
        this.checkSuspiciousPatterns(transaction),
        this.validateTransactionAmount(transaction),
      ]);

      return checks.every(check => check);
    } catch (error) {
      logger.error('Error validating token transaction', { error, transaction });
      return false;
    }
  }

  // Check daily earning limits
  public async checkDailyLimits(userId: string, source: TokenSource): Promise<boolean> {
    const today = new Date();
    const dailyEarnings = await this.dependencies.fraudRepository.getDailyEarnings(userId, today);

    // Check global daily limit
    if (dailyEarnings >= config.game.maxDailyTokens) {
      await this.reportFraud(userId, FraudType.EXCESSIVE_EARNINGS, FraudSeverity.MEDIUM, {
        dailyEarnings,
        limit: config.game.maxDailyTokens,
        source,
      });
      return false;
    }

    // Check source-specific limits
    const sourceLimit = this.getSourceDailyLimit(source);
    if (sourceLimit > 0) {
      const sourceEarnings = await this.getSourceDailyEarnings(userId, source, today);
      if (sourceEarnings >= sourceLimit) {
        return false;
      }
    }

    return true;
  }

  // Validate game session for fraud
  public async validateGameSession(session: GameSession): Promise<boolean> {
    if (!config.fraud.enabled) return true;

    try {
      const checks = await Promise.all([
        this.checkGameDuration(session),
        this.checkScoreValidity(session),
        this.checkRapidGameplay(session.userId),
        this.validateTouchPatterns(session),
      ]);

      const isValid = checks.every(check => check);
      
      if (!isValid) {
        await this.updateUserBehaviorMetrics(session.userId, session);
      }

      return isValid;
    } catch (error) {
      logger.error('Error validating game session', { error, session });
      return false;
    }
  }

  // Check for bot behavior patterns
  public async detectBotBehavior(userId: string): Promise<boolean> {
    const metrics = await this.dependencies.fraudRepository.getUserBehaviorMetrics(userId);
    if (!metrics) return false;

    const suspiciousIndicators = [];

    // Check for too consistent timing
    if (this.isTimingTooConsistent(metrics.timesBetweenSessions)) {
      suspiciousIndicators.push('consistent_timing');
    }

    // Check for perfect game ratio
    if (metrics.perfectGameRatio > 0.8 && metrics.sessionCount > 10) {
      suspiciousIndicators.push('high_perfect_ratio');
    }

    // Check for lack of touch pattern variation
    if (this.isTouchPatternTooConsistent(metrics.touchPatterns)) {
      suspiciousIndicators.push('consistent_touch_patterns');
    }

    // Check for rapid gameplay
    if (metrics.gamesPerSession > config.fraud.maxGamesPerHour / 2) {
      suspiciousIndicators.push('rapid_gameplay');
    }

    if (suspiciousIndicators.length >= 2) {
      await this.reportFraud(userId, FraudType.BOT_BEHAVIOR, FraudSeverity.HIGH, {
        indicators: suspiciousIndicators,
        metrics,
      });
      return true;
    }

    return false;
  }

  // Validate device fingerprint
  public async validateDeviceFingerprint(
    userId: string,
    fingerprint: string,
    deviceInfo: any
  ): Promise<boolean> {
    const existingFingerprint = await this.dependencies.fraudRepository.getDeviceFingerprint(
      userId,
      fingerprint
    );

    if (!existingFingerprint) {
      // New device - save fingerprint
      await this.dependencies.fraudRepository.saveDeviceFingerprint({
        userId,
        fingerprint,
        deviceInfo,
        firstSeen: new Date(),
        lastSeen: new Date(),
        isActive: true,
      });
      return true;
    }

    // Check for device info changes
    if (this.hasSignificantDeviceChanges(existingFingerprint.deviceInfo, deviceInfo)) {
      await this.reportFraud(userId, FraudType.DEVICE_MANIPULATION, FraudSeverity.MEDIUM, {
        oldDeviceInfo: existingFingerprint.deviceInfo,
        newDeviceInfo: deviceInfo,
        fingerprint,
      });
      return false;
    }

    // Update last seen
    existingFingerprint.lastSeen = new Date();
    await this.dependencies.fraudRepository.saveDeviceFingerprint(existingFingerprint);

    return true;
  }

  // Report suspicious activity
  public async reportSuspiciousActivity(
    userId: string,
    activity: string,
    details: any
  ): Promise<void> {
    securityLogger.suspiciousActivity(userId, activity, details);
    
    await this.reportFraud(
      userId,
      FraudType.SUSPICIOUS_PATTERN,
      FraudSeverity.LOW,
      { activity, details }
    );
  }

  // Private methods
  private async checkRapidTransactions(transaction: TokenTransactionEntity): Promise<boolean> {
    const recentTransactions = await this.dependencies.fraudRepository.getRecentTransactions(
      transaction.userId,
      5 // Last 5 minutes
    );

    if (recentTransactions.length > 10) {
      await this.reportFraud(
        transaction.userId,
        FraudType.RAPID_GAMEPLAY,
        FraudSeverity.MEDIUM,
        { recentTransactionCount: recentTransactions.length }
      );
      return false;
    }

    return true;
  }

  private async checkSuspiciousPatterns(transaction: TokenTransactionEntity): Promise<boolean> {
    // Check for duplicate transactions
    const duplicates = await this.findDuplicateTransactions(transaction);
    if (duplicates.length > 0) {
      await this.reportFraud(
        transaction.userId,
        FraudType.DUPLICATE_ACTIONS,
        FraudSeverity.HIGH,
        { duplicateTransactions: duplicates }
      );
      return false;
    }

    return true;
  }

  private async validateTransactionAmount(transaction: TokenTransactionEntity): Promise<boolean> {
    // Check if amount is reasonable for the source
    const maxAmount = this.getMaxAmountForSource(transaction.source);
    if (transaction.amount > maxAmount) {
      await this.reportFraud(
        transaction.userId,
        FraudType.IMPOSSIBLE_SCORE,
        FraudSeverity.HIGH,
        { amount: transaction.amount, maxExpected: maxAmount, source: transaction.source }
      );
      return false;
    }

    return true;
  }

  private async checkGameDuration(session: GameSession): Promise<boolean> {
    if (session.durationSeconds < config.game.minGameDurationSeconds) {
      await this.reportFraud(
        session.userId,
        FraudType.TIME_MANIPULATION,
        FraudSeverity.HIGH,
        { duration: session.durationSeconds, minimum: config.game.minGameDurationSeconds }
      );
      return false;
    }

    return true;
  }

  private async checkScoreValidity(session: GameSession): Promise<boolean> {
    // Implement game-specific score validation
    const maxPossibleScore = this.calculateMaxPossibleScore(session);
    
    if (session.score > maxPossibleScore) {
      await this.reportFraud(
        session.userId,
        FraudType.IMPOSSIBLE_SCORE,
        FraudSeverity.CRITICAL,
        { score: session.score, maxPossible: maxPossibleScore, gameType: session.gameType }
      );
      return false;
    }

    return true;
  }

  private async checkRapidGameplay(userId: string): Promise<boolean> {
    const currentHour = new Date();
    const gameCount = await this.dependencies.fraudRepository.getHourlyGameCount(userId, currentHour);

    if (gameCount > config.fraud.maxGamesPerHour) {
      await this.reportFraud(
        userId,
        FraudType.RAPID_GAMEPLAY,
        FraudSeverity.MEDIUM,
        { gamesThisHour: gameCount, limit: config.fraud.maxGamesPerHour }
      );
      return false;
    }

    return true;
  }

  private async validateTouchPatterns(session: GameSession): Promise<boolean> {
    // Implement touch pattern analysis
    // This would analyze touch coordinates, timing, pressure, etc.
    return true; // Placeholder
  }

  private async updateUserBehaviorMetrics(userId: string, session: GameSession): Promise<void> {
    let metrics = await this.dependencies.fraudRepository.getUserBehaviorMetrics(userId);
    
    if (!metrics) {
      metrics = {
        userId,
        sessionCount: 0,
        averageSessionDuration: 0,
        gamesPerSession: 0,
        averageScore: 0,
        perfectGameRatio: 0,
        timesBetweenSessions: [],
        deviceFingerprints: [],
        ipAddresses: [],
        userAgents: [],
        touchPatterns: [],
        lastActivity: new Date(),
      };
    }

    // Update metrics
    metrics.sessionCount++;
    metrics.averageSessionDuration = this.updateAverage(
      metrics.averageSessionDuration,
      session.durationSeconds,
      metrics.sessionCount
    );
    metrics.averageScore = this.updateAverage(
      metrics.averageScore,
      session.score,
      metrics.sessionCount
    );

    if (session.isPerfect) {
      metrics.perfectGameRatio = this.updateAverage(
        metrics.perfectGameRatio,
        1,
        metrics.sessionCount
      );
    }

    metrics.lastActivity = new Date();

    await this.dependencies.fraudRepository.updateUserBehaviorMetrics(metrics);
  }

  private async reportFraud(
    userId: string,
    type: FraudType,
    severity: FraudSeverity,
    evidence: Record<string, any>
  ): Promise<void> {
    const report: FraudReport = {
      id: this.generateReportId(),
      userId,
      type,
      severity,
      description: this.generateFraudDescription(type, evidence),
      evidence,
      status: 'pending',
      createdAt: new Date(),
    };

    await this.dependencies.fraudRepository.createReport(report);

    // Take immediate action for critical fraud
    if (severity === FraudSeverity.CRITICAL) {
      await this.takeFraudAction(userId, type, severity);
    }

    // Notify admins for high severity
    if ([FraudSeverity.HIGH, FraudSeverity.CRITICAL].includes(severity)) {
      await this.dependencies.notificationService.notifyAdmins(
        `Fraud detected: ${type}`,
        { userId, severity, evidence }
      );
    }

    logger.warn('Fraud detected', { userId, type, severity, evidence });
  }

  private async takeFraudAction(
    userId: string,
    type: FraudType,
    severity: FraudSeverity
  ): Promise<void> {
    switch (severity) {
      case FraudSeverity.CRITICAL:
        await this.dependencies.userRepository.suspendUser(
          userId,
          `Critical fraud detected: ${type}`,
          24 * 60 * 60 * 1000 // 24 hours
        );
        break;
      case FraudSeverity.HIGH:
        await this.dependencies.userRepository.flagUser(userId, `fraud_${type}`);
        await this.dependencies.notificationService.sendUserWarning(
          userId,
          'Suspicious activity detected on your account'
        );
        break;
    }
  }

  // Helper methods
  private getSourceDailyLimit(source: TokenSource): number {
    const limits = {
      [TokenSource.AD_VIEW]: config.game.maxDailyAdViews * 5, // 5 tokens per ad
      [TokenSource.GAME_COMPLETION]: config.game.maxDailyTokens * 0.8,
      [TokenSource.SURVEY_COMPLETION]: 500,
      [TokenSource.DAILY_MISSION]: 300,
    };

    return limits[source] || 0;
  }

  private async getSourceDailyEarnings(
    userId: string,
    source: TokenSource,
    date: Date
  ): Promise<number> {
    // Implementation would query transactions by source
    return 0; // Placeholder
  }

  private getMaxAmountForSource(source: TokenSource): number {
    const maxAmounts = {
      [TokenSource.GAME_COMPLETION]: 200,
      [TokenSource.AD_VIEW]: 10,
      [TokenSource.SURVEY_COMPLETION]: 500,
      [TokenSource.DAILY_MISSION]: 200,
      [TokenSource.ACHIEVEMENT]: 1000,
    };

    return maxAmounts[source] || 100;
  }

  private calculateMaxPossibleScore(session: GameSession): number {
    // Game-specific max score calculation
    const baseScores = {
      matching: 1000,
      sequence: 1500,
      timing: 2000,
      puzzle: 2500,
    };

    const baseScore = baseScores[session.gameType as keyof typeof baseScores] || 1000;
    return baseScore * (1 + session.difficulty * 0.2);
  }

  private isTimingTooConsistent(times: number[]): boolean {
    if (times.length < 5) return false;
    
    const variance = this.calculateVariance(times);
    return variance < 1000; // Less than 1 second variance
  }

  private isTouchPatternTooConsistent(patterns: TouchMetrics[]): boolean {
    // Analyze touch pattern consistency
    return false; // Placeholder
  }

  private hasSignificantDeviceChanges(oldInfo: any, newInfo: any): boolean {
    const significantFields = ['platform', 'model', 'osVersion', 'screenResolution'];
    return significantFields.some(field => oldInfo[field] !== newInfo[field]);
  }

  private async findDuplicateTransactions(transaction: TokenTransactionEntity): Promise<any[]> {
    // Implementation would find transactions with same reference ID and amount
    return []; // Placeholder
  }

  private updateAverage(currentAvg: number, newValue: number, count: number): number {
    return (currentAvg * (count - 1) + newValue) / count;
  }

  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private generateReportId(): string {
    return `fraud_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateFraudDescription(type: FraudType, evidence: Record<string, any>): string {
    const descriptions = {
      [FraudType.SUSPICIOUS_PATTERN]: 'Suspicious user behavior pattern detected',
      [FraudType.IMPOSSIBLE_SCORE]: `Impossible score detected: ${evidence.score}`,
      [FraudType.BOT_BEHAVIOR]: 'Bot-like behavior patterns identified',
      [FraudType.RAPID_GAMEPLAY]: `Rapid gameplay detected: ${evidence.gamesThisHour} games/hour`,
      [FraudType.DEVICE_MANIPULATION]: 'Device fingerprint manipulation detected',
      [FraudType.TIME_MANIPULATION]: `Game duration too short: ${evidence.duration}s`,
      [FraudType.EXCESSIVE_EARNINGS]: `Daily earning limit exceeded: ${evidence.dailyEarnings}`,
      [FraudType.DUPLICATE_ACTIONS]: 'Duplicate transaction detected',
    };

    return descriptions[type] || 'Fraud detected';
  }
}
