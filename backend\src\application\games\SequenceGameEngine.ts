import { GameEngine, GameConfig, GameInput, GameResult, GameType, ScoringSystem } from '@/domain/entities/Game';

// Sequence Game Specific Types
interface SequenceElement {
  id: string;
  value: number | string;
  type: 'number' | 'color' | 'shape' | 'pattern';
  position: number;
}

interface SequenceGameState {
  sequence: SequenceElement[];
  userInput: SequenceElement[];
  currentStep: number;
  totalSteps: number;
  isShowingSequence: boolean;
  isWaitingForInput: boolean;
  correctSteps: number;
  mistakes: number;
  maxMistakes: number;
  timeRemaining: number;
  isCompleted: boolean;
  isFailed: boolean;
  sequenceType: string;
}

interface SequenceGameConfig extends GameConfig {
  sequenceLength: number;
  sequenceType: 'arithmetic' | 'geometric' | 'fibonacci' | 'color_pattern' | 'shape_pattern' | 'mixed';
  showDuration: number;
  inputTimeLimit: number;
  allowMistakes: boolean;
}

export class SequenceGameEngine implements GameEngine {
  private config!: SequenceGameConfig;
  private state!: SequenceGameState;
  private startTime!: number;
  private sequenceShowTimer: number = 0;
  private inputTimer: number = 0;

  // Pattern definitions
  private colorPatterns = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan'];
  private shapePatterns = ['circle', 'square', 'triangle', 'diamond', 'star', 'hexagon', 'heart', 'arrow'];

  public initialize(config: GameConfig): void {
    this.config = {
      ...config,
      sequenceLength: this.calculateSequenceLength(config.difficulty),
      sequenceType: this.selectSequenceType(config.difficulty),
      showDuration: Math.max(1, 4 - Math.floor(config.difficulty / 3)),
      inputTimeLimit: Math.max(5, 15 - config.difficulty),
      allowMistakes: config.difficulty <= 6,
    } as SequenceGameConfig;

    this.generateSequence();
    this.resetState();
  }

  public start(): void {
    this.startTime = Date.now();
    this.state.timeRemaining = this.config.timeLimit || 120;
    this.startSequenceDisplay();
  }

  public processInput(input: GameInput): void {
    if (this.state.isCompleted || this.state.isFailed) return;
    if (this.state.isShowingSequence) return;

    switch (input.type) {
      case 'sequence_input':
        this.handleSequenceInput(input.data.element);
        break;
      case 'skip_step':
        this.handleSkipStep();
        break;
      case 'restart_sequence':
        this.restartSequence();
        break;
    }

    this.checkGameCompletion();
  }

  public update(deltaTime: number): void {
    if (this.state.isCompleted || this.state.isFailed) return;

    // Update global timer
    if (this.config.timeLimit) {
      this.state.timeRemaining -= deltaTime;
      if (this.state.timeRemaining <= 0) {
        this.state.isFailed = true;
        return;
      }
    }

    // Update sequence display timer
    if (this.state.isShowingSequence) {
      this.sequenceShowTimer -= deltaTime;
      if (this.sequenceShowTimer <= 0) {
        this.state.isShowingSequence = false;
        this.state.isWaitingForInput = true;
        this.inputTimer = this.config.inputTimeLimit;
      }
    }

    // Update input timer
    if (this.state.isWaitingForInput) {
      this.inputTimer -= deltaTime;
      if (this.inputTimer <= 0) {
        this.handleTimeout();
      }
    }
  }

  public getState(): SequenceGameState {
    return { 
      ...this.state,
      sequence: this.state.isShowingSequence ? this.state.sequence : [],
    };
  }

  public getResult(): GameResult {
    const duration = (Date.now() - this.startTime) / 1000;
    const maxScore = this.calculateMaxScore();
    const score = this.calculateScore();
    const isPerfect = this.state.correctSteps === this.state.totalSteps && this.state.mistakes === 0;
    const tokensEarned = ScoringSystem.calculateTokenReward(score, maxScore, isPerfect, this.config.difficulty);

    return {
      score,
      maxScore,
      isCompleted: this.state.isCompleted,
      isPerfect,
      durationSeconds: Math.floor(duration),
      tokensEarned,
      metadata: {
        correctSteps: this.state.correctSteps,
        totalSteps: this.state.totalSteps,
        mistakes: this.state.mistakes,
        accuracy: this.state.correctSteps / this.state.totalSteps,
        sequenceType: this.state.sequenceType,
        sequenceLength: this.config.sequenceLength,
        timeRemaining: this.state.timeRemaining,
        difficulty: this.config.difficulty,
        gameType: GameType.SEQUENCE,
      },
    };
  }

  public isCompleted(): boolean {
    return this.state.isCompleted;
  }

  public isFailed(): boolean {
    return this.state.isFailed;
  }

  public cleanup(): void {
    // Clean up any resources if needed
  }

  private calculateSequenceLength(difficulty: number): number {
    // Difficulty 1-2: 4 elements, 3-4: 6 elements, 5-6: 8 elements, 7-8: 10 elements, 9-10: 12 elements
    return Math.min(12, 4 + Math.floor((difficulty - 1) / 2) * 2);
  }

  private selectSequenceType(difficulty: number): 'arithmetic' | 'geometric' | 'fibonacci' | 'color_pattern' | 'shape_pattern' | 'mixed' {
    const types = ['arithmetic', 'geometric', 'fibonacci', 'color_pattern', 'shape_pattern', 'mixed'];
    
    if (difficulty <= 2) {
      return ['arithmetic', 'color_pattern'][Math.floor(Math.random() * 2)] as any;
    } else if (difficulty <= 5) {
      return types.slice(0, 4)[Math.floor(Math.random() * 4)] as any;
    } else {
      return types[Math.floor(Math.random() * types.length)] as any;
    }
  }

  private generateSequence(): void {
    const sequence: SequenceElement[] = [];
    
    switch (this.config.sequenceType) {
      case 'arithmetic':
        sequence.push(...this.generateArithmeticSequence());
        break;
      case 'geometric':
        sequence.push(...this.generateGeometricSequence());
        break;
      case 'fibonacci':
        sequence.push(...this.generateFibonacciSequence());
        break;
      case 'color_pattern':
        sequence.push(...this.generateColorPattern());
        break;
      case 'shape_pattern':
        sequence.push(...this.generateShapePattern());
        break;
      case 'mixed':
        sequence.push(...this.generateMixedPattern());
        break;
    }

    this.state.sequence = sequence;
    this.state.totalSteps = sequence.length;
  }

  private generateArithmeticSequence(): SequenceElement[] {
    const start = Math.floor(Math.random() * 10) + 1;
    const step = Math.floor(Math.random() * 5) + 1;
    const sequence: SequenceElement[] = [];

    for (let i = 0; i < this.config.sequenceLength; i++) {
      sequence.push({
        id: `seq_${i}`,
        value: start + (i * step),
        type: 'number',
        position: i,
      });
    }

    return sequence;
  }

  private generateGeometricSequence(): SequenceElement[] {
    const start = Math.floor(Math.random() * 5) + 1;
    const ratio = Math.floor(Math.random() * 3) + 2;
    const sequence: SequenceElement[] = [];

    for (let i = 0; i < this.config.sequenceLength; i++) {
      sequence.push({
        id: `seq_${i}`,
        value: start * Math.pow(ratio, i),
        type: 'number',
        position: i,
      });
    }

    return sequence;
  }

  private generateFibonacciSequence(): SequenceElement[] {
    const sequence: SequenceElement[] = [];
    let a = 1, b = 1;

    for (let i = 0; i < this.config.sequenceLength; i++) {
      sequence.push({
        id: `seq_${i}`,
        value: i < 2 ? 1 : a + b,
        type: 'number',
        position: i,
      });

      if (i >= 2) {
        const temp = a + b;
        a = b;
        b = temp;
      }
    }

    return sequence;
  }

  private generateColorPattern(): SequenceElement[] {
    const patternLength = Math.min(4, Math.floor(this.config.sequenceLength / 2));
    const pattern = this.colorPatterns
      .sort(() => Math.random() - 0.5)
      .slice(0, patternLength);
    
    const sequence: SequenceElement[] = [];
    
    for (let i = 0; i < this.config.sequenceLength; i++) {
      sequence.push({
        id: `seq_${i}`,
        value: pattern[i % pattern.length],
        type: 'color',
        position: i,
      });
    }

    return sequence;
  }

  private generateShapePattern(): SequenceElement[] {
    const patternLength = Math.min(3, Math.floor(this.config.sequenceLength / 3));
    const pattern = this.shapePatterns
      .sort(() => Math.random() - 0.5)
      .slice(0, patternLength);
    
    const sequence: SequenceElement[] = [];
    
    for (let i = 0; i < this.config.sequenceLength; i++) {
      sequence.push({
        id: `seq_${i}`,
        value: pattern[i % pattern.length],
        type: 'shape',
        position: i,
      });
    }

    return sequence;
  }

  private generateMixedPattern(): SequenceElement[] {
    // Combine different pattern types
    const types = ['number', 'color', 'shape'];
    const sequence: SequenceElement[] = [];
    
    for (let i = 0; i < this.config.sequenceLength; i++) {
      const type = types[i % types.length];
      let value: number | string;
      
      switch (type) {
        case 'number':
          value = (i + 1) * 2;
          break;
        case 'color':
          value = this.colorPatterns[i % this.colorPatterns.length];
          break;
        case 'shape':
          value = this.shapePatterns[i % this.shapePatterns.length];
          break;
        default:
          value = i + 1;
      }
      
      sequence.push({
        id: `seq_${i}`,
        value,
        type: type as any,
        position: i,
      });
    }

    return sequence;
  }

  private resetState(): void {
    this.state = {
      sequence: [],
      userInput: [],
      currentStep: 0,
      totalSteps: 0,
      isShowingSequence: false,
      isWaitingForInput: false,
      correctSteps: 0,
      mistakes: 0,
      maxMistakes: this.config.allowMistakes ? Math.floor(this.config.sequenceLength / 2) : 0,
      timeRemaining: this.config.timeLimit || 120,
      isCompleted: false,
      isFailed: false,
      sequenceType: this.config.sequenceType,
    };
  }

  private startSequenceDisplay(): void {
    this.state.isShowingSequence = true;
    this.sequenceShowTimer = this.config.showDuration;
  }

  private handleSequenceInput(element: SequenceElement): void {
    if (!this.state.isWaitingForInput) return;

    const expectedElement = this.state.sequence[this.state.currentStep];
    
    if (this.isElementMatch(element, expectedElement)) {
      this.state.correctSteps++;
      this.state.currentStep++;
      this.state.userInput.push(element);
      
      if (this.state.currentStep >= this.state.totalSteps) {
        this.state.isCompleted = true;
        this.state.isWaitingForInput = false;
      } else {
        // Continue to next step
        this.inputTimer = this.config.inputTimeLimit;
      }
    } else {
      this.state.mistakes++;
      
      if (!this.config.allowMistakes || this.state.mistakes >= this.state.maxMistakes) {
        this.state.isFailed = true;
        this.state.isWaitingForInput = false;
      } else {
        // Allow retry
        this.inputTimer = this.config.inputTimeLimit;
      }
    }
  }

  private handleSkipStep(): void {
    if (!this.config.allowMistakes) return;
    
    this.state.mistakes++;
    this.state.currentStep++;
    
    if (this.state.mistakes >= this.state.maxMistakes) {
      this.state.isFailed = true;
    } else if (this.state.currentStep >= this.state.totalSteps) {
      this.state.isCompleted = true;
    }
    
    this.state.isWaitingForInput = false;
  }

  private handleTimeout(): void {
    this.state.mistakes++;
    
    if (!this.config.allowMistakes || this.state.mistakes >= this.state.maxMistakes) {
      this.state.isFailed = true;
    } else {
      // Show sequence again
      this.startSequenceDisplay();
    }
    
    this.state.isWaitingForInput = false;
  }

  private restartSequence(): void {
    this.state.currentStep = 0;
    this.state.userInput = [];
    this.startSequenceDisplay();
  }

  private isElementMatch(input: SequenceElement, expected: SequenceElement): boolean {
    return input.value === expected.value && input.type === expected.type;
  }

  private checkGameCompletion(): void {
    if (this.state.currentStep >= this.state.totalSteps && this.state.correctSteps > 0) {
      this.state.isCompleted = true;
    }
  }

  private calculateMaxScore(): number {
    return ScoringSystem.calculateBaseScore(
      GameType.SEQUENCE,
      this.config.difficulty,
      1.0
    ) + ScoringSystem.calculateTimeBonus(
      this.config.timeLimit || 120,
      0,
      true
    ) + ScoringSystem.calculatePerfectBonus(
      true,
      this.config.difficulty
    );
  }

  private calculateScore(): number {
    if (this.state.correctSteps === 0) return 0;
    
    const performance = this.state.correctSteps / this.state.totalSteps;
    const accuracy = this.state.correctSteps / (this.state.correctSteps + this.state.mistakes);
    const adjustedPerformance = performance * accuracy;
    
    const baseScore = ScoringSystem.calculateBaseScore(
      GameType.SEQUENCE,
      this.config.difficulty,
      adjustedPerformance
    );
    
    const timeBonus = ScoringSystem.calculateTimeBonus(
      this.config.timeLimit || 120,
      (Date.now() - this.startTime) / 1000,
      true
    );
    
    const isPerfect = this.state.correctSteps === this.state.totalSteps && this.state.mistakes === 0;
    const perfectBonus = ScoringSystem.calculatePerfectBonus(
      isPerfect,
      this.config.difficulty
    );
    
    return baseScore + timeBonus + perfectBonus;
  }
}
