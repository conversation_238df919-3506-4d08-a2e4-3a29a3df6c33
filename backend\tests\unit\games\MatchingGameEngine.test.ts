import { MatchingGameEngine } from '@/application/games/MatchingGameEngine';
import { GameType } from '@/domain/entities/Game';

describe('MatchingGameEngine', () => {
  let gameEngine: MatchingGameEngine;

  beforeEach(() => {
    gameEngine = new MatchingGameEngine();
  });

  describe('initialize', () => {
    it('should initialize game with correct configuration', () => {
      const config = {
        type: GameType.MATCHING,
        difficulty: 3,
        timeLimit: 60,
      };

      gameEngine.initialize(config);
      const state = gameEngine.getState();

      expect(state.totalPairs).toBeGreaterThan(0);
      expect(state.pairs).toHaveLength(state.totalPairs * 2);
      expect(state.matchedPairs).toBe(0);
      expect(state.selectedItems).toHaveLength(0);
      expect(state.isCompleted).toBe(false);
      expect(state.isFailed).toBe(false);
    });

    it('should adjust pair count based on difficulty', () => {
      const easyConfig = { type: GameType.MATCHING, difficulty: 1 };
      const hardConfig = { type: GameType.MATCHING, difficulty: 8 };

      gameEngine.initialize(easyConfig);
      const easyState = gameEngine.getState();

      gameEngine.initialize(hardConfig);
      const hardState = gameEngine.getState();

      expect(hardState.totalPairs).toBeGreaterThan(easyState.totalPairs);
    });

    it('should generate valid pairs', () => {
      const config = { type: GameType.MATCHING, difficulty: 3 };
      gameEngine.initialize(config);
      const state = gameEngine.getState();

      // Check that each value appears exactly twice
      const valueCount = new Map<string, number>();
      state.pairs.forEach(pair => {
        const count = valueCount.get(pair.value) || 0;
        valueCount.set(pair.value, count + 1);
      });

      valueCount.forEach(count => {
        expect(count).toBe(2);
      });
    });
  });

  describe('processInput', () => {
    beforeEach(() => {
      const config = { type: GameType.MATCHING, difficulty: 2 };
      gameEngine.initialize(config);
      gameEngine.start();
    });

    it('should select items correctly', () => {
      const state = gameEngine.getState();
      const firstItemId = state.pairs[0].id;

      gameEngine.processInput({
        type: 'select_item',
        data: { itemId: firstItemId },
        timestamp: Date.now(),
      });

      const updatedState = gameEngine.getState();
      expect(updatedState.selectedItems).toContain(firstItemId);
    });

    it('should not select already matched items', () => {
      const state = gameEngine.getState();
      const firstPair = state.pairs.filter(p => p.value === state.pairs[0].value);
      
      // Mark items as matched
      firstPair.forEach(item => item.matched = true);

      gameEngine.processInput({
        type: 'select_item',
        data: { itemId: firstPair[0].id },
        timestamp: Date.now(),
      });

      const updatedState = gameEngine.getState();
      expect(updatedState.selectedItems).not.toContain(firstPair[0].id);
    });

    it('should limit selection to 2 items', () => {
      const state = gameEngine.getState();
      const itemIds = state.pairs.slice(0, 3).map(p => p.id);

      // Try to select 3 items
      itemIds.forEach(itemId => {
        gameEngine.processInput({
          type: 'select_item',
          data: { itemId },
          timestamp: Date.now(),
        });
      });

      const updatedState = gameEngine.getState();
      expect(updatedState.selectedItems).toHaveLength(2);
    });

    it('should deselect items correctly', () => {
      const state = gameEngine.getState();
      const firstItemId = state.pairs[0].id;

      // Select item
      gameEngine.processInput({
        type: 'select_item',
        data: { itemId: firstItemId },
        timestamp: Date.now(),
      });

      // Deselect item
      gameEngine.processInput({
        type: 'deselect_item',
        data: { itemId: firstItemId },
        timestamp: Date.now(),
      });

      const updatedState = gameEngine.getState();
      expect(updatedState.selectedItems).not.toContain(firstItemId);
    });

    it('should reset selection correctly', () => {
      const state = gameEngine.getState();
      const itemIds = state.pairs.slice(0, 2).map(p => p.id);

      // Select 2 items
      itemIds.forEach(itemId => {
        gameEngine.processInput({
          type: 'select_item',
          data: { itemId },
          timestamp: Date.now(),
        });
      });

      // Reset selection
      gameEngine.processInput({
        type: 'reset_selection',
        data: {},
        timestamp: Date.now(),
      });

      const updatedState = gameEngine.getState();
      expect(updatedState.selectedItems).toHaveLength(0);
    });
  });

  describe('game completion', () => {
    beforeEach(() => {
      const config = { type: GameType.MATCHING, difficulty: 1 };
      gameEngine.initialize(config);
      gameEngine.start();
    });

    it('should complete game when all pairs are matched', () => {
      const state = gameEngine.getState();
      
      // Manually match all pairs for testing
      const uniqueValues = [...new Set(state.pairs.map(p => p.value))];
      uniqueValues.forEach(value => {
        const pairItems = state.pairs.filter(p => p.value === value);
        pairItems.forEach(item => item.matched = true);
      });

      // Trigger completion check
      gameEngine.processInput({
        type: 'reset_selection',
        data: {},
        timestamp: Date.now(),
      });

      expect(gameEngine.isCompleted()).toBe(true);
    });

    it('should calculate correct score', () => {
      const config = { type: GameType.MATCHING, difficulty: 3 };
      gameEngine.initialize(config);
      gameEngine.start();

      // Simulate perfect game
      const state = gameEngine.getState();
      state.matchedPairs = state.totalPairs;
      state.attempts = state.totalPairs;
      state.isCompleted = true;

      const result = gameEngine.getResult();

      expect(result.score).toBeGreaterThan(0);
      expect(result.isCompleted).toBe(true);
      expect(result.tokensEarned).toBeGreaterThan(0);
      expect(result.metadata.accuracy).toBe(1);
    });

    it('should detect perfect game', () => {
      const config = { type: GameType.MATCHING, difficulty: 2 };
      gameEngine.initialize(config);
      gameEngine.start();

      const state = gameEngine.getState();
      state.matchedPairs = state.totalPairs;
      state.attempts = state.totalPairs; // Perfect attempts
      state.isCompleted = true;

      const result = gameEngine.getResult();

      expect(result.isPerfect).toBe(true);
    });

    it('should not be perfect with extra attempts', () => {
      const config = { type: GameType.MATCHING, difficulty: 2 };
      gameEngine.initialize(config);
      gameEngine.start();

      const state = gameEngine.getState();
      state.matchedPairs = state.totalPairs;
      state.attempts = state.totalPairs + 2; // Extra attempts
      state.isCompleted = true;

      const result = gameEngine.getResult();

      expect(result.isPerfect).toBe(false);
    });
  });

  describe('time management', () => {
    beforeEach(() => {
      const config = { 
        type: GameType.MATCHING, 
        difficulty: 3,
        timeLimit: 30 
      };
      gameEngine.initialize(config);
      gameEngine.start();
    });

    it('should track time remaining', () => {
      const initialState = gameEngine.getState();
      expect(initialState.timeRemaining).toBe(30);

      // Simulate 10 seconds passing
      gameEngine.update(10);

      const updatedState = gameEngine.getState();
      expect(updatedState.timeRemaining).toBe(20);
    });

    it('should fail game when time runs out', () => {
      // Simulate time running out
      gameEngine.update(35);

      const state = gameEngine.getState();
      expect(state.timeRemaining).toBe(0);
      expect(gameEngine.isFailed()).toBe(true);
    });

    it('should not update when game is completed', () => {
      // Mark game as completed
      const state = gameEngine.getState();
      state.isCompleted = true;

      const initialTime = state.timeRemaining;
      gameEngine.update(10);

      expect(state.timeRemaining).toBe(initialTime);
    });
  });

  describe('difficulty scaling', () => {
    it('should have more pairs for higher difficulty', () => {
      const difficulties = [1, 3, 5, 7, 10];
      const pairCounts: number[] = [];

      difficulties.forEach(difficulty => {
        const config = { type: GameType.MATCHING, difficulty };
        gameEngine.initialize(config);
        const state = gameEngine.getState();
        pairCounts.push(state.totalPairs);
      });

      // Check that pair count generally increases with difficulty
      for (let i = 1; i < pairCounts.length; i++) {
        expect(pairCounts[i]).toBeGreaterThanOrEqual(pairCounts[i - 1]);
      }
    });

    it('should allow mistakes for lower difficulties', () => {
      const easyConfig = { type: GameType.MATCHING, difficulty: 2 };
      const hardConfig = { type: GameType.MATCHING, difficulty: 8 };

      gameEngine.initialize(easyConfig);
      const easyState = gameEngine.getState();

      gameEngine.initialize(hardConfig);
      const hardState = gameEngine.getState();

      expect(easyState.maxAttempts).toBeGreaterThan(easyState.totalPairs);
      expect(hardState.maxAttempts).toBeGreaterThanOrEqual(hardState.totalPairs);
    });
  });
});
