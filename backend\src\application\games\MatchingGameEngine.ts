import { GameEngine, GameConfig, GameInput, GameResult, GameType, ScoringSystem } from '@/domain/entities/Game';

// Matching Game Specific Types
interface MatchingPair {
  id: string;
  category: string;
  value: string;
  matched: boolean;
}

interface MatchingGameState {
  pairs: MatchingPair[];
  selectedItems: string[];
  matchedPairs: number;
  totalPairs: number;
  attempts: number;
  maxAttempts: number;
  timeRemaining: number;
  isCompleted: boolean;
  isFailed: boolean;
}

interface MatchingGameConfig extends GameConfig {
  pairCount: number;
  categories: string[];
  allowMistakes: boolean;
  showPreview: boolean;
  previewDuration: number;
}

export class MatchingGameEngine implements GameEngine {
  private config!: MatchingGameConfig;
  private state!: MatchingGameState;
  private startTime!: number;
  private gameData: Record<string, string[]> = {
    'programming': [
      'HTML', 'CSS', 'JavaScript', 'TypeScript', 'React', 'Vue', 'Angular', 'Node.js',
      'Python', 'Java', 'C++', 'Go', 'Rust', 'Swift', 'Kotlin', 'PHP'
    ],
    'colors': [
      'Red', 'Blue', 'Green', 'Yellow', '<PERSON>', 'Orange', 'Pink', 'Cyan',
      'Magenta', 'Lime', 'Indigo', 'Violet', 'Turquoise', 'Coral', 'Gold', 'Silver'
    ],
    'shapes': [
      'Circle', 'Square', 'Triangle', 'Rectangle', 'Pentagon', 'Hexagon', 'Octagon', 'Star',
      'Diamond', 'Oval', 'Heart', 'Arrow', 'Cross', 'Spiral', 'Zigzag', 'Wave'
    ],
    'numbers': [
      '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16'
    ]
  };

  public initialize(config: GameConfig): void {
    this.config = {
      ...config,
      pairCount: this.calculatePairCount(config.difficulty),
      categories: this.selectCategories(config.difficulty),
      allowMistakes: config.difficulty <= 5,
      showPreview: config.difficulty <= 3,
      previewDuration: Math.max(2, 6 - config.difficulty),
    } as MatchingGameConfig;

    this.generatePairs();
    this.resetState();
  }

  public start(): void {
    this.startTime = Date.now();
    this.state.timeRemaining = this.config.timeLimit || 60;
  }

  public processInput(input: GameInput): void {
    if (this.state.isCompleted || this.state.isFailed) return;

    switch (input.type) {
      case 'select_item':
        this.handleItemSelection(input.data.itemId);
        break;
      case 'deselect_item':
        this.handleItemDeselection(input.data.itemId);
        break;
      case 'confirm_match':
        this.handleMatchConfirmation();
        break;
      case 'reset_selection':
        this.resetSelection();
        break;
    }

    this.checkGameCompletion();
  }

  public update(deltaTime: number): void {
    if (this.state.isCompleted || this.state.isFailed) return;

    if (this.config.timeLimit) {
      this.state.timeRemaining -= deltaTime;
      if (this.state.timeRemaining <= 0) {
        this.state.isFailed = true;
        this.state.timeRemaining = 0;
      }
    }
  }

  public getState(): MatchingGameState {
    return { ...this.state };
  }

  public getResult(): GameResult {
    const duration = (Date.now() - this.startTime) / 1000;
    const maxScore = this.calculateMaxScore();
    const score = this.calculateScore();
    const isPerfect = this.state.matchedPairs === this.state.totalPairs && this.state.attempts === this.state.totalPairs;
    const tokensEarned = ScoringSystem.calculateTokenReward(score, maxScore, isPerfect, this.config.difficulty);

    return {
      score,
      maxScore,
      isCompleted: this.state.isCompleted,
      isPerfect,
      durationSeconds: Math.floor(duration),
      tokensEarned,
      metadata: {
        matchedPairs: this.state.matchedPairs,
        totalPairs: this.state.totalPairs,
        attempts: this.state.attempts,
        accuracy: this.state.matchedPairs / this.state.attempts,
        timeRemaining: this.state.timeRemaining,
        difficulty: this.config.difficulty,
        gameType: GameType.MATCHING,
      },
    };
  }

  public isCompleted(): boolean {
    return this.state.isCompleted;
  }

  public isFailed(): boolean {
    return this.state.isFailed;
  }

  public cleanup(): void {
    // Clean up any resources if needed
  }

  private calculatePairCount(difficulty: number): number {
    // Difficulty 1-2: 4 pairs, 3-4: 6 pairs, 5-6: 8 pairs, 7-8: 10 pairs, 9-10: 12 pairs
    return Math.min(12, 4 + Math.floor((difficulty - 1) / 2) * 2);
  }

  private selectCategories(difficulty: number): string[] {
    const allCategories = Object.keys(this.gameData);
    const categoryCount = Math.min(allCategories.length, Math.ceil(difficulty / 3));
    
    // Shuffle and select categories
    const shuffled = [...allCategories].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, categoryCount);
  }

  private generatePairs(): void {
    const pairs: MatchingPair[] = [];
    const usedValues = new Set<string>();
    
    for (let i = 0; i < this.config.pairCount; i++) {
      const category = this.config.categories[i % this.config.categories.length];
      const availableValues = this.gameData[category].filter(v => !usedValues.has(v));
      
      if (availableValues.length === 0) continue;
      
      const value = availableValues[Math.floor(Math.random() * availableValues.length)];
      usedValues.add(value);
      
      // Create two items for the pair
      pairs.push({
        id: `${i}_a`,
        category,
        value,
        matched: false,
      });
      
      pairs.push({
        id: `${i}_b`,
        category,
        value,
        matched: false,
      });
    }
    
    // Shuffle the pairs
    this.state.pairs = pairs.sort(() => Math.random() - 0.5);
  }

  private resetState(): void {
    this.state = {
      pairs: [],
      selectedItems: [],
      matchedPairs: 0,
      totalPairs: this.config.pairCount,
      attempts: 0,
      maxAttempts: this.config.allowMistakes ? this.config.pairCount * 3 : this.config.pairCount,
      timeRemaining: this.config.timeLimit || 60,
      isCompleted: false,
      isFailed: false,
    };
  }

  private handleItemSelection(itemId: string): void {
    if (this.state.selectedItems.includes(itemId)) return;
    if (this.state.selectedItems.length >= 2) return;
    
    const item = this.state.pairs.find(p => p.id === itemId);
    if (!item || item.matched) return;
    
    this.state.selectedItems.push(itemId);
    
    // Auto-confirm when 2 items are selected
    if (this.state.selectedItems.length === 2) {
      setTimeout(() => this.handleMatchConfirmation(), 500);
    }
  }

  private handleItemDeselection(itemId: string): void {
    this.state.selectedItems = this.state.selectedItems.filter(id => id !== itemId);
  }

  private handleMatchConfirmation(): void {
    if (this.state.selectedItems.length !== 2) return;
    
    const [item1Id, item2Id] = this.state.selectedItems;
    const item1 = this.state.pairs.find(p => p.id === item1Id);
    const item2 = this.state.pairs.find(p => p.id === item2Id);
    
    if (!item1 || !item2) return;
    
    this.state.attempts++;
    
    // Check if items match
    if (item1.value === item2.value && item1.category === item2.category) {
      // Match found
      item1.matched = true;
      item2.matched = true;
      this.state.matchedPairs++;
    } else if (!this.config.allowMistakes) {
      // Game over on mistake if not allowed
      this.state.isFailed = true;
    }
    
    // Check if max attempts reached
    if (this.state.attempts >= this.state.maxAttempts) {
      this.state.isFailed = true;
    }
    
    this.resetSelection();
  }

  private resetSelection(): void {
    this.state.selectedItems = [];
  }

  private checkGameCompletion(): void {
    if (this.state.matchedPairs === this.state.totalPairs) {
      this.state.isCompleted = true;
    }
  }

  private calculateMaxScore(): number {
    return ScoringSystem.calculateBaseScore(
      GameType.MATCHING,
      this.config.difficulty,
      1.0 // Perfect performance
    ) + ScoringSystem.calculateTimeBonus(
      this.config.timeLimit || 60,
      0, // No time used (perfect)
      true
    ) + ScoringSystem.calculatePerfectBonus(
      true,
      this.config.difficulty
    );
  }

  private calculateScore(): number {
    if (this.state.matchedPairs === 0) return 0;
    
    const performance = this.state.matchedPairs / this.state.totalPairs;
    const accuracy = this.state.matchedPairs / Math.max(1, this.state.attempts);
    const adjustedPerformance = performance * accuracy;
    
    const baseScore = ScoringSystem.calculateBaseScore(
      GameType.MATCHING,
      this.config.difficulty,
      adjustedPerformance
    );
    
    const timeBonus = ScoringSystem.calculateTimeBonus(
      this.config.timeLimit || 60,
      (Date.now() - this.startTime) / 1000,
      true
    );
    
    const isPerfect = this.state.matchedPairs === this.state.totalPairs && 
                     this.state.attempts === this.state.totalPairs;
    const perfectBonus = ScoringSystem.calculatePerfectBonus(
      isPerfect,
      this.config.difficulty
    );
    
    return baseScore + timeBonus + perfectBonus;
  }
}
