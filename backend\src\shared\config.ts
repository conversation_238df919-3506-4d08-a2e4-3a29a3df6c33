import dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = z.object({
  // Server configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  
  // Database configuration
  DATABASE_URL: z.string().min(1, 'Database URL is required'),
  
  // Redis configuration
  REDIS_URL: z.string().optional(),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().transform(Number).default('6379'),
  REDIS_PASSWORD: z.string().optional(),
  
  // JWT configuration
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT refresh secret must be at least 32 characters'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('30d'),
  
  // Firebase configuration
  FIREBASE_PROJECT_ID: z.string().optional(),
  FIREBASE_PRIVATE_KEY: z.string().optional(),
  FIREBASE_CLIENT_EMAIL: z.string().optional(),
  
  // AdMob configuration
  ADMOB_APP_ID: z.string().optional(),
  ADMOB_PUBLISHER_ID: z.string().optional(),
  
  // Stripe configuration
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  
  // Email configuration
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  
  // Security configuration
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // Game configuration
  MAX_DAILY_AD_VIEWS: z.string().transform(Number).default('50'),
  MAX_DAILY_TOKENS: z.string().transform(Number).default('1000'),
  MIN_GAME_DURATION_SECONDS: z.string().transform(Number).default('10'),
  
  // Fraud detection
  FRAUD_DETECTION_ENABLED: z.string().transform(Boolean).default('true'),
  MAX_GAMES_PER_HOUR: z.string().transform(Number).default('20'),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // CORS
  CORS_ALLOWED_ORIGINS: z.string().default('http://localhost:3000,http://localhost:8080'),
});

// Validate environment variables
const env = envSchema.parse(process.env);

// Application configuration
export const config = {
  // Environment
  env: env.NODE_ENV,
  port: env.PORT,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  isTest: env.NODE_ENV === 'test',
  
  // Database
  database: {
    url: env.DATABASE_URL,
  },
  
  // Redis
  redis: {
    url: env.REDIS_URL,
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
  },
  
  // JWT
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshSecret: env.JWT_REFRESH_SECRET,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },
  
  // Firebase
  firebase: {
    projectId: env.FIREBASE_PROJECT_ID,
    privateKey: env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    clientEmail: env.FIREBASE_CLIENT_EMAIL,
  },
  
  // AdMob
  admob: {
    appId: env.ADMOB_APP_ID,
    publisherId: env.ADMOB_PUBLISHER_ID,
  },
  
  // Stripe
  stripe: {
    secretKey: env.STRIPE_SECRET_KEY,
    webhookSecret: env.STRIPE_WEBHOOK_SECRET,
  },
  
  // Email
  email: {
    host: env.SMTP_HOST,
    port: env.SMTP_PORT,
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
  },
  
  // Security
  security: {
    bcryptRounds: env.BCRYPT_ROUNDS,
    rateLimitWindowMs: env.RATE_LIMIT_WINDOW_MS,
    rateLimitMaxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  },
  
  // Game settings
  game: {
    maxDailyAdViews: env.MAX_DAILY_AD_VIEWS,
    maxDailyTokens: env.MAX_DAILY_TOKENS,
    minGameDurationSeconds: env.MIN_GAME_DURATION_SECONDS,
  },
  
  // Fraud detection
  fraud: {
    enabled: env.FRAUD_DETECTION_ENABLED,
    maxGamesPerHour: env.MAX_GAMES_PER_HOUR,
  },
  
  // Logging
  logging: {
    level: env.LOG_LEVEL,
  },
  
  // CORS
  cors: {
    allowedOrigins: env.CORS_ALLOWED_ORIGINS.split(',').map(origin => origin.trim()),
  },
  
  // Token rewards configuration
  rewards: {
    gameCompletion: {
      base: 10,
      difficultyMultiplier: 1.5,
      perfectBonus: 2.0,
    },
    adViewing: {
      rewardPerAd: 5,
      dailyLimit: env.MAX_DAILY_AD_VIEWS,
      cooldownMinutes: 5,
    },
    surveys: {
      minReward: 20,
      maxReward: 100,
    },
    dailyMissions: {
      easy: 25,
      medium: 50,
      hard: 100,
    },
  },
  
  // Game types configuration
  gameTypes: {
    matching: {
      name: 'Matching Game',
      baseScore: 100,
      timeBonus: true,
      difficultyLevels: 5,
    },
    sequence: {
      name: 'Sequence Game',
      baseScore: 150,
      timeBonus: true,
      difficultyLevels: 8,
    },
    timing: {
      name: 'Timing Game',
      baseScore: 200,
      timeBonus: false,
      difficultyLevels: 10,
    },
    puzzle: {
      name: 'Puzzle Game',
      baseScore: 250,
      timeBonus: false,
      difficultyLevels: 6,
    },
  },
} as const;

// Type exports
export type Config = typeof config;
export type GameType = keyof typeof config.gameTypes;
