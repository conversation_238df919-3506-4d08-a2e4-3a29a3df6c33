import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/config/app_config.dart';
import 'core/di/injection.dart';
import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/bloc_observer.dart';
import 'core/utils/error_handler.dart';
import 'shared/services/analytics_service.dart';
import 'shared/services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize error handling
  await ErrorHandler.initialize();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize Mobile Ads
  await MobileAds.instance.initialize();

  // Initialize Hive
  await Hive.initFlutter();

  // Initialize dependency injection
  await configureDependencies();

  // Initialize services
  await _initializeServices();

  // Set up BLoC observer
  Bloc.observer = AppBlocObserver();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.black,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  runApp(const CodeRushApp());
}

Future<void> _initializeServices() async {
  try {
    // Initialize analytics
    await getIt<AnalyticsService>().initialize();
    
    // Initialize notifications
    await getIt<NotificationService>().initialize();
    
    // Log app start
    await getIt<AnalyticsService>().logEvent('app_start');
  } catch (e) {
    // Log error but don't prevent app from starting
    debugPrint('Error initializing services: $e');
  }
}

class CodeRushApp extends StatelessWidget {
  const CodeRushApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: AppConfig.appName,
      debugShowCheckedModeBanner: false,
      
      // Theme configuration
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.dark, // Default to dark theme for cybertech aesthetic
      
      // Router configuration
      routerConfig: AppRouter.router,
      
      // Localization
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('es', 'ES'),
      ],
      
      // Builder for global error handling and overlays
      builder: (context, child) {
        return MediaQuery(
          // Ensure text scaling doesn't break UI
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(
              MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.2),
            ),
          ),
          child: child ?? const SizedBox.shrink(),
        );
      },
    );
  }
}

class AppBlocObserver extends BlocObserver {
  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    debugPrint('BLoC Created: ${bloc.runtimeType}');
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    if (AppConfig.isDebugMode) {
      debugPrint('BLoC Change: ${bloc.runtimeType} - $change');
    }
  }

  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    if (AppConfig.isDebugMode) {
      debugPrint('BLoC Transition: ${bloc.runtimeType} - $transition');
    }
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    debugPrint('BLoC Error: ${bloc.runtimeType} - $error');
    
    // Log error to analytics
    getIt<AnalyticsService>().logError(
      error.toString(),
      stackTrace: stackTrace,
      additionalData: {
        'bloc_type': bloc.runtimeType.toString(),
      },
    );
  }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    debugPrint('BLoC Closed: ${bloc.runtimeType}');
  }
}
