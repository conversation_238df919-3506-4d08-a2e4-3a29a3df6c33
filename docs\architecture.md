# Arquitectura de Code Rush

## 🏗️ Visión General de la Arquitectura

Code Rush sigue una arquitectura de microservicios modular con separación clara entre frontend móvil, backend API y servicios auxiliares.

## 📱 Frontend Mobile (Flutter)

### Arquitectura Clean Architecture + BLoC

```
mobile/lib/
├── core/
│   ├── config/           # Configuración de la app
│   ├── constants/        # Constantes globales
│   ├── errors/          # Manejo de errores
│   ├── network/         # Cliente HTTP
│   ├── storage/         # Almacenamiento local
│   └── utils/           # Utilidades generales
├── features/
│   ├── auth/            # Autenticación
│   │   ├── data/        # Repositorios y fuentes de datos
│   │   ├── domain/      # Entidades y casos de uso
│   │   └── presentation/ # UI y BLoC
│   ├── games/           # Minijuegos
│   ├── rewards/         # Sistema de recompensas
│   ├── profile/         # Perfil de usuario
│   └── leaderboard/     # Clasificaciones
└── shared/
    ├── widgets/         # Widgets reutilizables
    ├── themes/          # Temas y estilos
    └── services/        # Servicios compartidos
```

### Patrones de Diseño
- **BLo<PERSON> Pattern**: Gestión de estado
- **Repository Pattern**: Abstracción de datos
- **Dependency Injection**: GetIt
- **Clean Architecture**: Separación de capas

## 🔧 Backend (Node.js + TypeScript)

### Arquitectura Hexagonal

```
backend/src/
├── application/         # Casos de uso
│   ├── auth/           # Lógica de autenticación
│   ├── games/          # Lógica de juegos
│   ├── rewards/        # Lógica de recompensas
│   └── analytics/      # Análisis y métricas
├── domain/             # Entidades y reglas de negocio
│   ├── entities/       # Modelos de dominio
│   ├── repositories/   # Interfaces de repositorios
│   └── services/       # Servicios de dominio
├── infrastructure/     # Implementaciones externas
│   ├── database/       # PostgreSQL + Prisma
│   ├── cache/          # Redis
│   ├── auth/           # JWT + Firebase
│   ├── payments/       # Stripe/PayPal
│   └── monitoring/     # Logs y métricas
└── presentation/       # API REST/GraphQL
    ├── controllers/    # Controladores HTTP
    ├── middleware/     # Middlewares
    ├── routes/         # Definición de rutas
    └── validators/     # Validación de entrada
```

### Tecnologías Clave
- **Express.js**: Framework web
- **Prisma**: ORM para PostgreSQL
- **Redis**: Cache y sesiones
- **JWT**: Autenticación stateless
- **Zod**: Validación de esquemas
- **Winston**: Logging

## 🗄️ Base de Datos

### Esquema Principal (PostgreSQL)

```sql
-- Usuarios
users (
  id UUID PRIMARY KEY,
  email VARCHAR UNIQUE,
  username VARCHAR UNIQUE,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  is_active BOOLEAN,
  last_login TIMESTAMP
)

-- Perfiles de usuario
user_profiles (
  user_id UUID REFERENCES users(id),
  display_name VARCHAR,
  avatar_url VARCHAR,
  level INTEGER DEFAULT 1,
  total_ib_tokens INTEGER DEFAULT 0,
  games_played INTEGER DEFAULT 0,
  achievements JSONB
)

-- Sesiones de juego
game_sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  game_type VARCHAR,
  score INTEGER,
  duration_seconds INTEGER,
  ib_tokens_earned INTEGER,
  completed_at TIMESTAMP,
  metadata JSONB
)

-- Transacciones de tokens
token_transactions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  type VARCHAR, -- 'earned', 'spent', 'withdrawn'
  amount INTEGER,
  source VARCHAR, -- 'game', 'ad', 'survey', 'bonus'
  reference_id UUID,
  created_at TIMESTAMP
)

-- Misiones diarias
daily_missions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  mission_type VARCHAR,
  target_value INTEGER,
  current_progress INTEGER,
  reward_tokens INTEGER,
  expires_at TIMESTAMP,
  completed_at TIMESTAMP
)
```

### Cache (Redis)
- **Sesiones de usuario**: `user:session:{userId}`
- **Leaderboards**: `leaderboard:{gameType}:{period}`
- **Rate limiting**: `rate_limit:{userId}:{action}`
- **Configuración de juegos**: `game_config:{gameType}`

## 🎮 Sistema de Minijuegos

### Arquitectura Modular

```typescript
interface GameEngine {
  initialize(config: GameConfig): void;
  start(): void;
  update(deltaTime: number): void;
  handleInput(input: GameInput): void;
  getState(): GameState;
  end(): GameResult;
}

interface GameConfig {
  type: GameType;
  difficulty: number;
  timeLimit?: number;
  customParams?: Record<string, any>;
}

enum GameType {
  MATCHING = 'matching',
  SEQUENCE = 'sequence',
  TIMING = 'timing',
  PUZZLE = 'puzzle'
}
```

### Tipos de Minijuegos

1. **Emparejamiento (Matching)**
   - Conectar elementos relacionados
   - Validación de pares correctos
   - Sistema de puntuación por velocidad

2. **Secuencias (Sequence)**
   - Completar patrones lógicos
   - Progresión de dificultad
   - Memoria y lógica

3. **Tiempo Límite (Timing)**
   - Desafíos contra reloj
   - Reflejos y precisión
   - Multiplicadores de puntuación

4. **Puzzles (Puzzle)**
   - Resolución de problemas
   - Creatividad y estrategia
   - Múltiples soluciones

## 🏆 Sistema de Recompensas

### IB Tokens - Economía del Juego

```typescript
interface TokenEconomy {
  // Fuentes de ingresos
  gameCompletion: {
    baseReward: number;
    difficultyMultiplier: number;
    perfectBonusMultiplier: number;
  };
  
  adViewing: {
    rewardPerAd: number;
    dailyLimit: number;
    cooldownMinutes: number;
  };
  
  surveys: {
    rewardRange: [number, number];
    verificationRequired: boolean;
  };
  
  // Gastos y canjes
  powerUps: Record<string, number>;
  cosmetics: Record<string, number>;
  realWorldRewards: Record<string, number>;
}
```

### Validaciones de Seguridad
- **Rate Limiting**: Límites por usuario y acción
- **Detección de Patrones**: Análisis de comportamiento
- **Verificación de Dispositivos**: Fingerprinting
- **Validación Temporal**: Tiempos mínimos de juego

## 🔒 Sistema Antifraude

### Métricas de Detección

```typescript
interface FraudDetection {
  // Patrones sospechosos
  gameCompletionTime: {
    min: number;
    max: number;
    averageDeviation: number;
  };
  
  // Comportamiento del usuario
  sessionPatterns: {
    averageSessionDuration: number;
    gamesPerSession: number;
    timesBetweenSessions: number;
  };
  
  // Validaciones técnicas
  deviceFingerprint: string;
  ipGeolocation: string;
  userAgent: string;
  touchPatterns: TouchMetrics[];
}
```

### Acciones Preventivas
1. **Alertas Automáticas**: Notificación de comportamientos anómalos
2. **Suspensión Temporal**: Bloqueo preventivo de cuentas
3. **Verificación Manual**: Revisión humana de casos complejos
4. **Machine Learning**: Modelos predictivos de fraude

## 📊 Monetización y Analytics

### Integración de AdMob

```typescript
interface AdIntegration {
  // Tipos de anuncios
  interstitial: {
    frequency: number; // Cada X juegos
    placement: 'between_games' | 'level_complete';
  };
  
  rewarded: {
    tokenMultiplier: number;
    dailyLimit: number;
    verification: 'completion' | 'engagement';
  };
  
  banner: {
    placement: 'bottom' | 'top';
    refreshRate: number;
  };
}
```

### Analytics y Métricas

```typescript
interface GameAnalytics {
  // Métricas de usuario
  retention: {
    day1: number;
    day7: number;
    day30: number;
  };
  
  // Métricas de juego
  gameMetrics: {
    averageSessionDuration: number;
    gamesPerSession: number;
    completionRate: number;
    difficultyProgression: number;
  };
  
  // Métricas de monetización
  monetization: {
    adViewRate: number;
    tokenEarnRate: number;
    rewardRedemptionRate: number;
    ltv: number; // Lifetime Value
  };
}
```

## 🚀 Escalabilidad y Performance

### Estrategias de Escalamiento

1. **Horizontal Scaling**
   - Load balancers (NGINX/AWS ALB)
   - Múltiples instancias de API
   - Database read replicas

2. **Caching Strategy**
   - Redis para datos frecuentes
   - CDN para assets estáticos
   - Application-level caching

3. **Database Optimization**
   - Índices optimizados
   - Particionamiento de tablas
   - Connection pooling

4. **Monitoring y Observabilidad**
   - Prometheus + Grafana
   - Error tracking (Sentry)
   - Performance monitoring (New Relic)

## 🔄 CI/CD y DevOps

### Pipeline de Desarrollo

```yaml
# GitHub Actions Workflow
stages:
  - lint_and_test
  - build_images
  - deploy_staging
  - integration_tests
  - deploy_production
  - post_deploy_verification
```

### Containerización

```dockerfile
# Backend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

Esta arquitectura proporciona una base sólida, escalable y mantenible para Code Rush, con separación clara de responsabilidades y patrones de diseño probados en la industria.
