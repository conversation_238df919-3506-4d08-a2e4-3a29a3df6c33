# Environment Configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/code_rush_db

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-at-least-32-characters-long
JWT_REFRESH_EXPIRES_IN=30d

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Firebase Private Key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# AdMob Configuration
ADMOB_APP_ID=ca-app-pub-xxxxxxxxxxxxxxxx~xxxxxxxxxx
ADMOB_PUBLISHER_ID=pub-xxxxxxxxxxxxxxxx

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Game Configuration
MAX_DAILY_AD_VIEWS=50
MAX_DAILY_TOKENS=1000
MIN_GAME_DURATION_SECONDS=10

# Fraud Detection
FRAUD_DETECTION_ENABLED=true
MAX_GAMES_PER_HOUR=20

# Logging
LOG_LEVEL=info

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:3001

# External APIs
SURVEY_PROVIDER_API_KEY=your-survey-provider-api-key
ANALYTICS_API_KEY=your-analytics-api-key

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Cache TTL (in seconds)
CACHE_TTL_SHORT=300
CACHE_TTL_MEDIUM=1800
CACHE_TTL_LONG=3600

# Rate Limiting
RATE_LIMIT_GAME_SESSIONS=30
RATE_LIMIT_TOKEN_TRANSACTIONS=100
RATE_LIMIT_AD_VIEWS=60

# Monitoring
SENTRY_DSN=https://<EMAIL>/xxxxxxx
NEW_RELIC_LICENSE_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Development Tools
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true
