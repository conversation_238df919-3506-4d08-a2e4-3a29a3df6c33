import { 
  TokenTransactionEntity, 
  TokenBalanceEntity, 
  TokenTransactionType, 
  TokenSource, 
  TokenSpendingCategory,
  TokenRewardCalculator 
} from '@/domain/entities/Token';
import { createLogger } from '@/shared/logger';
import { config } from '@/shared/config';

const logger = createLogger('TokenService');

export interface TokenServiceDependencies {
  tokenRepository: TokenRepository;
  userRepository: UserRepository;
  fraudDetectionService: FraudDetectionService;
  notificationService: NotificationService;
}

export interface TokenRepository {
  createTransaction(transaction: TokenTransactionEntity): Promise<TokenTransactionEntity>;
  getBalance(userId: string): Promise<TokenBalanceEntity | null>;
  updateBalance(balance: TokenBalanceEntity): Promise<TokenBalanceEntity>;
  getTransactionHistory(userId: string, limit?: number, offset?: number): Promise<TokenTransactionEntity[]>;
  getDailyEarnings(userId: string, date: Date): Promise<number>;
  getWeeklyEarnings(userId: string, startDate: Date): Promise<number>;
  getMonthlyEarnings(userId: string, month: number, year: number): Promise<number>;
}

export interface UserRepository {
  getUserById(id: string): Promise<User | null>;
  updateUserLevel(userId: string, level: number): Promise<void>;
}

export interface FraudDetectionService {
  validateTokenTransaction(transaction: TokenTransactionEntity): Promise<boolean>;
  checkDailyLimits(userId: string, source: TokenSource): Promise<boolean>;
  reportSuspiciousActivity(userId: string, activity: string, details: any): Promise<void>;
}

export interface NotificationService {
  sendTokenEarnedNotification(userId: string, amount: number, source: string): Promise<void>;
  sendLevelUpNotification(userId: string, newLevel: number): Promise<void>;
  sendDailyLimitNotification(userId: string, source: string): Promise<void>;
}

export interface User {
  id: string;
  level: number;
  tier: string;
  isActive: boolean;
  createdAt: Date;
}

export class TokenService {
  constructor(private dependencies: TokenServiceDependencies) {}

  // Award tokens for game completion
  public async awardGameTokens(
    userId: string,
    gameType: string,
    difficulty: number,
    score: number,
    maxScore: number,
    isPerfect: boolean,
    duration: number,
    gameSessionId: string
  ): Promise<TokenTransactionEntity> {
    logger.info('Awarding game tokens', { userId, gameType, difficulty, score });

    // Validate user
    const user = await this.dependencies.userRepository.getUserById(userId);
    if (!user || !user.isActive) {
      throw new Error('User not found or inactive');
    }

    // Calculate reward
    const amount = TokenRewardCalculator.calculateGameReward(
      gameType,
      difficulty,
      score,
      maxScore,
      isPerfect,
      duration
    );

    // Create transaction
    const transaction = new TokenTransactionEntity(
      this.generateTransactionId(),
      {
        userId,
        type: TokenTransactionType.EARNED,
        amount,
        source: TokenSource.GAME_COMPLETION,
        referenceId: gameSessionId,
        description: `Game completion: ${gameType} (Level ${difficulty})`,
        metadata: {
          gameType,
          difficulty,
          score,
          maxScore,
          isPerfect,
          duration,
          scoreRatio: score / maxScore,
        },
      }
    );

    // Validate transaction
    const isValid = await this.dependencies.fraudDetectionService.validateTokenTransaction(transaction);
    if (!isValid) {
      logger.warn('Token transaction failed fraud validation', { userId, transaction });
      throw new Error('Transaction failed security validation');
    }

    // Process transaction
    return this.processTransaction(transaction);
  }

  // Award tokens for ad viewing
  public async awardAdTokens(
    userId: string,
    adType: string,
    adProvider: string,
    adId: string
  ): Promise<TokenTransactionEntity> {
    logger.info('Awarding ad tokens', { userId, adType, adProvider });

    // Check daily limits
    const withinLimits = await this.dependencies.fraudDetectionService.checkDailyLimits(
      userId,
      TokenSource.AD_VIEW
    );
    if (!withinLimits) {
      await this.dependencies.notificationService.sendDailyLimitNotification(userId, 'ad_viewing');
      throw new Error('Daily ad viewing limit reached');
    }

    // Get user for level calculation
    const user = await this.dependencies.userRepository.getUserById(userId);
    if (!user || !user.isActive) {
      throw new Error('User not found or inactive');
    }

    // Calculate reward
    const amount = TokenRewardCalculator.calculateAdReward(adType, user.level);

    // Create transaction
    const transaction = new TokenTransactionEntity(
      this.generateTransactionId(),
      {
        userId,
        type: TokenTransactionType.EARNED,
        amount,
        source: TokenSource.AD_VIEW,
        referenceId: adId,
        description: `Ad viewed: ${adType}`,
        metadata: {
          adType,
          adProvider,
          userLevel: user.level,
        },
      }
    );

    return this.processTransaction(transaction);
  }

  // Award tokens for survey completion
  public async awardSurveyTokens(
    userId: string,
    surveyId: string,
    surveyLength: number,
    surveyType: string
  ): Promise<TokenTransactionEntity> {
    logger.info('Awarding survey tokens', { userId, surveyId, surveyType });

    // Get user for tier calculation
    const user = await this.dependencies.userRepository.getUserById(userId);
    if (!user || !user.isActive) {
      throw new Error('User not found or inactive');
    }

    // Calculate reward
    const amount = TokenRewardCalculator.calculateSurveyReward(
      surveyLength,
      surveyType,
      user.tier
    );

    // Create transaction
    const transaction = new TokenTransactionEntity(
      this.generateTransactionId(),
      {
        userId,
        type: TokenTransactionType.EARNED,
        amount,
        source: TokenSource.SURVEY_COMPLETION,
        referenceId: surveyId,
        description: `Survey completed: ${surveyType}`,
        metadata: {
          surveyLength,
          surveyType,
          userTier: user.tier,
        },
      }
    );

    return this.processTransaction(transaction);
  }

  // Spend tokens
  public async spendTokens(
    userId: string,
    amount: number,
    category: TokenSpendingCategory,
    itemId: string,
    description?: string
  ): Promise<TokenTransactionEntity> {
    logger.info('Processing token spending', { userId, amount, category, itemId });

    // Get current balance
    const balance = await this.getBalance(userId);
    if (balance.availableTokens < amount) {
      throw new Error('Insufficient tokens');
    }

    // Create transaction
    const transaction = new TokenTransactionEntity(
      this.generateTransactionId(),
      {
        userId,
        type: TokenTransactionType.SPENT,
        amount,
        source: TokenSource.PURCHASE,
        category,
        referenceId: itemId,
        description: description || `Purchase: ${category}`,
        metadata: {
          category,
          itemId,
          balanceBefore: balance.availableTokens,
        },
      }
    );

    return this.processTransaction(transaction);
  }

  // Get user balance
  public async getBalance(userId: string): Promise<TokenBalanceEntity> {
    let balance = await this.dependencies.tokenRepository.getBalance(userId);
    
    if (!balance) {
      // Create initial balance
      balance = new TokenBalanceEntity({
        userId,
        totalTokens: 0,
        availableTokens: 0,
        pendingTokens: 0,
        lifetimeEarned: 0,
        lifetimeSpent: 0,
      });
      
      balance = await this.dependencies.tokenRepository.updateBalance(balance);
    }

    return balance;
  }

  // Get transaction history
  public async getTransactionHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<TokenTransactionEntity[]> {
    return this.dependencies.tokenRepository.getTransactionHistory(userId, limit, offset);
  }

  // Get earnings summary
  public async getEarningsSummary(userId: string): Promise<EarningsSummary> {
    const today = new Date();
    const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const [dailyEarnings, weeklyEarnings, monthlyEarnings] = await Promise.all([
      this.dependencies.tokenRepository.getDailyEarnings(userId, today),
      this.dependencies.tokenRepository.getWeeklyEarnings(userId, weekStart),
      this.dependencies.tokenRepository.getMonthlyEarnings(userId, today.getMonth() + 1, today.getFullYear()),
    ]);

    return {
      daily: dailyEarnings,
      weekly: weeklyEarnings,
      monthly: monthlyEarnings,
      dailyLimit: config.game.maxDailyTokens,
      remainingDaily: Math.max(0, config.game.maxDailyTokens - dailyEarnings),
    };
  }

  // Check if user can earn more tokens today
  public async canEarnTokens(userId: string, source: TokenSource): Promise<boolean> {
    return this.dependencies.fraudDetectionService.checkDailyLimits(userId, source);
  }

  // Process pending tokens (for delayed rewards)
  public async processPendingTokens(userId: string, amount: number): Promise<void> {
    const balance = await this.getBalance(userId);
    balance.confirmPendingTokens(amount);
    await this.dependencies.tokenRepository.updateBalance(balance);
    
    logger.info('Processed pending tokens', { userId, amount });
  }

  // Private methods
  private async processTransaction(transaction: TokenTransactionEntity): Promise<TokenTransactionEntity> {
    // Save transaction
    const savedTransaction = await this.dependencies.tokenRepository.createTransaction(transaction);
    
    // Update balance
    const balance = await this.getBalance(transaction.userId);
    
    if (transaction.isEarning()) {
      balance.addTokens(transaction.amount);
      
      // Send notification
      await this.dependencies.notificationService.sendTokenEarnedNotification(
        transaction.userId,
        transaction.amount,
        transaction.source
      );
    } else {
      balance.spendTokens(transaction.amount);
    }
    
    await this.dependencies.tokenRepository.updateBalance(balance);
    
    // Check for level up
    await this.checkLevelUp(transaction.userId, balance);
    
    logger.info('Transaction processed successfully', { 
      transactionId: savedTransaction.id,
      userId: transaction.userId,
      amount: transaction.amount,
      type: transaction.type,
    });
    
    return savedTransaction;
  }

  private async checkLevelUp(userId: string, balance: TokenBalanceEntity): Promise<void> {
    const user = await this.dependencies.userRepository.getUserById(userId);
    if (!user) return;

    // Calculate new level based on lifetime earned tokens
    const newLevel = Math.floor(balance.lifetimeEarned / 1000) + 1;
    
    if (newLevel > user.level) {
      await this.dependencies.userRepository.updateUserLevel(userId, newLevel);
      await this.dependencies.notificationService.sendLevelUpNotification(userId, newLevel);
      
      logger.info('User leveled up', { userId, oldLevel: user.level, newLevel });
    }
  }

  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export interface EarningsSummary {
  daily: number;
  weekly: number;
  monthly: number;
  dailyLimit: number;
  remainingDaily: number;
}
