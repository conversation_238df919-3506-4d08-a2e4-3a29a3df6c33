import { z } from 'zod';

// Token Transaction Types
export enum TokenTransactionType {
  EARNED = 'earned',
  SPENT = 'spent',
  WITHDRAWN = 'withdrawn',
  BONUS = 'bonus',
  PENALTY = 'penalty',
  REFUND = 'refund'
}

// Token Sources
export enum TokenSource {
  GAME_COMPLETION = 'game',
  AD_VIEW = 'ad',
  SURVEY_COMPLETION = 'survey',
  DAILY_MISSION = 'daily_mission',
  ACHIEVEMENT = 'achievement',
  REFERRAL = 'referral',
  LOGIN_BONUS = 'login_bonus',
  PURCHASE = 'purchase',
  ADMIN_GRANT = 'admin_grant'
}

// Token Spending Categories
export enum TokenSpendingCategory {
  POWER_UPS = 'power_ups',
  COSMETICS = 'cosmetics',
  EXTRA_LIVES = 'extra_lives',
  HINTS = 'hints',
  REAL_WORLD_REWARDS = 'real_world_rewards',
  PREMIUM_FEATURES = 'premium_features'
}

// Validation Schemas
export const TokenTransactionSchema = z.object({
  userId: z.string().uuid(),
  type: z.nativeEnum(TokenTransactionType),
  amount: z.number().int().positive(),
  source: z.nativeEnum(TokenSource),
  category: z.nativeEnum(TokenSpendingCategory).optional(),
  referenceId: z.string().optional(),
  description: z.string().max(255).optional(),
  metadata: z.record(z.any()).optional(),
});

export const TokenBalanceSchema = z.object({
  userId: z.string().uuid(),
  totalTokens: z.number().int().min(0),
  availableTokens: z.number().int().min(0),
  pendingTokens: z.number().int().min(0),
  lifetimeEarned: z.number().int().min(0),
  lifetimeSpent: z.number().int().min(0),
});

// Type definitions
export type TokenTransaction = z.infer<typeof TokenTransactionSchema>;
export type TokenBalance = z.infer<typeof TokenBalanceSchema>;

// Token Transaction Entity
export class TokenTransactionEntity {
  public readonly id: string;
  public readonly userId: string;
  public readonly type: TokenTransactionType;
  public readonly amount: number;
  public readonly source: TokenSource;
  public readonly category?: TokenSpendingCategory;
  public readonly referenceId?: string;
  public readonly description?: string;
  public readonly metadata: Record<string, any>;
  public readonly createdAt: Date;

  constructor(
    id: string,
    data: TokenTransaction,
    createdAt: Date = new Date()
  ) {
    this.id = id;
    this.userId = data.userId;
    this.type = data.type;
    this.amount = data.amount;
    this.source = data.source;
    this.category = data.category;
    this.referenceId = data.referenceId;
    this.description = data.description;
    this.metadata = data.metadata || {};
    this.createdAt = createdAt;
  }

  public isEarning(): boolean {
    return [
      TokenTransactionType.EARNED,
      TokenTransactionType.BONUS,
      TokenTransactionType.REFUND
    ].includes(this.type);
  }

  public isSpending(): boolean {
    return [
      TokenTransactionType.SPENT,
      TokenTransactionType.WITHDRAWN,
      TokenTransactionType.PENALTY
    ].includes(this.type);
  }

  public getEffectiveAmount(): number {
    return this.isEarning() ? this.amount : -this.amount;
  }

  public toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      type: this.type,
      amount: this.amount,
      source: this.source,
      category: this.category,
      referenceId: this.referenceId,
      description: this.description,
      metadata: this.metadata,
      createdAt: this.createdAt,
      effectiveAmount: this.getEffectiveAmount(),
    };
  }
}

// Token Balance Entity
export class TokenBalanceEntity {
  public readonly userId: string;
  public totalTokens: number;
  public availableTokens: number;
  public pendingTokens: number;
  public lifetimeEarned: number;
  public lifetimeSpent: number;
  public lastUpdated: Date;

  constructor(data: TokenBalance) {
    this.userId = data.userId;
    this.totalTokens = data.totalTokens;
    this.availableTokens = data.availableTokens;
    this.pendingTokens = data.pendingTokens;
    this.lifetimeEarned = data.lifetimeEarned;
    this.lifetimeSpent = data.lifetimeSpent;
    this.lastUpdated = new Date();
  }

  public addTokens(amount: number, isPending: boolean = false): void {
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }

    if (isPending) {
      this.pendingTokens += amount;
    } else {
      this.availableTokens += amount;
      this.totalTokens += amount;
      this.lifetimeEarned += amount;
    }

    this.lastUpdated = new Date();
  }

  public spendTokens(amount: number): void {
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }

    if (this.availableTokens < amount) {
      throw new Error('Insufficient tokens');
    }

    this.availableTokens -= amount;
    this.totalTokens -= amount;
    this.lifetimeSpent += amount;
    this.lastUpdated = new Date();
  }

  public confirmPendingTokens(amount: number): void {
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }

    if (this.pendingTokens < amount) {
      throw new Error('Insufficient pending tokens');
    }

    this.pendingTokens -= amount;
    this.availableTokens += amount;
    this.totalTokens += amount;
    this.lifetimeEarned += amount;
    this.lastUpdated = new Date();
  }

  public cancelPendingTokens(amount: number): void {
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }

    if (this.pendingTokens < amount) {
      throw new Error('Insufficient pending tokens');
    }

    this.pendingTokens -= amount;
    this.lastUpdated = new Date();
  }

  public getSpendingPower(): number {
    return this.availableTokens;
  }

  public getNetWorth(): number {
    return this.totalTokens + this.pendingTokens;
  }

  public getSpendingRatio(): number {
    if (this.lifetimeEarned === 0) return 0;
    return this.lifetimeSpent / this.lifetimeEarned;
  }

  public toJSON() {
    return {
      userId: this.userId,
      totalTokens: this.totalTokens,
      availableTokens: this.availableTokens,
      pendingTokens: this.pendingTokens,
      lifetimeEarned: this.lifetimeEarned,
      lifetimeSpent: this.lifetimeSpent,
      lastUpdated: this.lastUpdated,
      spendingPower: this.getSpendingPower(),
      netWorth: this.getNetWorth(),
      spendingRatio: this.getSpendingRatio(),
    };
  }
}

// Token Reward Calculator
export class TokenRewardCalculator {
  // Game completion rewards
  public static calculateGameReward(
    gameType: string,
    difficulty: number,
    score: number,
    maxScore: number,
    isPerfect: boolean,
    duration: number
  ): number {
    const baseRewards = {
      matching: 10,
      sequence: 15,
      timing: 20,
      puzzle: 25,
    };

    const baseReward = baseRewards[gameType as keyof typeof baseRewards] || 10;
    const difficultyMultiplier = 1 + (difficulty - 1) * 0.1;
    const scoreRatio = score / maxScore;
    const perfectBonus = isPerfect ? 2 : 1;

    return Math.floor(baseReward * difficultyMultiplier * scoreRatio * perfectBonus);
  }

  // Ad viewing rewards
  public static calculateAdReward(adType: string, userLevel: number): number {
    const baseRewards = {
      interstitial: 3,
      rewarded: 5,
      banner: 1,
    };

    const baseReward = baseRewards[adType as keyof typeof baseRewards] || 3;
    const levelBonus = Math.floor(userLevel / 10) * 0.5;

    return Math.floor(baseReward * (1 + levelBonus));
  }

  // Survey completion rewards
  public static calculateSurveyReward(
    surveyLength: number,
    surveyType: string,
    userTier: string
  ): number {
    const baseReward = Math.max(20, surveyLength * 2);
    
    const typeMultipliers = {
      short: 1,
      medium: 1.5,
      long: 2,
      premium: 3,
    };

    const tierMultipliers = {
      bronze: 1,
      silver: 1.2,
      gold: 1.5,
      platinum: 2,
    };

    const typeMultiplier = typeMultipliers[surveyType as keyof typeof typeMultipliers] || 1;
    const tierMultiplier = tierMultipliers[userTier as keyof typeof tierMultipliers] || 1;

    return Math.floor(baseReward * typeMultiplier * tierMultiplier);
  }

  // Daily mission rewards
  public static calculateMissionReward(
    missionType: string,
    difficulty: string,
    streak: number
  ): number {
    const baseRewards = {
      easy: 25,
      medium: 50,
      hard: 100,
      expert: 200,
    };

    const baseReward = baseRewards[difficulty as keyof typeof baseRewards] || 25;
    const streakBonus = Math.min(streak * 0.1, 1); // Max 100% bonus

    return Math.floor(baseReward * (1 + streakBonus));
  }

  // Achievement rewards
  public static calculateAchievementReward(
    achievementTier: string,
    achievementCategory: string,
    rarity: number
  ): number {
    const tierRewards = {
      bronze: 50,
      silver: 100,
      gold: 200,
      platinum: 500,
      diamond: 1000,
    };

    const categoryMultipliers = {
      gameplay: 1,
      social: 1.2,
      progression: 1.5,
      special: 2,
    };

    const baseReward = tierRewards[achievementTier as keyof typeof tierRewards] || 50;
    const categoryMultiplier = categoryMultipliers[achievementCategory as keyof typeof categoryMultipliers] || 1;
    const rarityMultiplier = 1 + (rarity / 100);

    return Math.floor(baseReward * categoryMultiplier * rarityMultiplier);
  }

  // Referral rewards
  public static calculateReferralReward(
    referralType: string,
    referredUserLevel: number,
    isFirstTime: boolean
  ): number {
    const baseRewards = {
      signup: 100,
      first_game: 50,
      first_purchase: 200,
      milestone: 150,
    };

    const baseReward = baseRewards[referralType as keyof typeof baseRewards] || 50;
    const levelBonus = Math.floor(referredUserLevel / 5) * 10;
    const firstTimeBonus = isFirstTime ? 1.5 : 1;

    return Math.floor((baseReward + levelBonus) * firstTimeBonus);
  }
}
