# Guía de Desarrollo - Code Rush

## 🚀 Configuración del Entorno de Desarrollo

### Prerrequisitos

- **Node.js** 18+ y npm 8+
- **Flutter** 3.10+ y Dart 3.0+
- **PostgreSQL** 14+
- **Redis** 6+
- **Docker** y Docker Compose (opcional)
- **Git**

### Configuración Inicial

1. **Clonar el repositorio**
```bash
git clone https://github.com/tu-usuario/code-rush.git
cd code-rush
```

2. **Configurar variables de entorno**
```bash
# Backend
cd backend
cp .env.example .env
# Editar .env con tus configuraciones

# Mobile
cd ../mobile
cp .env.example .env
# Editar .env con tus configuraciones
```

3. **Instalar dependencias**
```bash
# Backend
cd backend
npm install

# Mobile
cd ../mobile
flutter pub get
```

4. **Configurar base de datos**
```bash
cd backend
npx prisma migrate dev
npx prisma db seed
```

### Desarrollo con Docker (Recomendado)

```bash
# Levantar todos los servicios
docker-compose up -d

# Ver logs
docker-compose logs -f backend

# Ejecutar migraciones
docker-compose exec backend npx prisma migrate dev

# Parar servicios
docker-compose down
```

### Desarrollo Local

1. **Backend**
```bash
cd backend
npm run dev
```

2. **Mobile (Android)**
```bash
cd mobile
flutter run
```

3. **Mobile (iOS)**
```bash
cd mobile
flutter run -d ios
```

## 🏗️ Estructura del Proyecto

### Backend (Node.js + TypeScript)

```
backend/
├── src/
│   ├── application/        # Casos de uso y servicios
│   │   ├── games/         # Lógica de juegos
│   │   ├── services/      # Servicios de aplicación
│   │   └── use-cases/     # Casos de uso específicos
│   ├── domain/            # Entidades y reglas de negocio
│   │   ├── entities/      # Modelos de dominio
│   │   ├── repositories/  # Interfaces de repositorios
│   │   └── services/      # Servicios de dominio
│   ├── infrastructure/    # Implementaciones externas
│   │   ├── database/      # PostgreSQL + Prisma
│   │   ├── cache/         # Redis
│   │   ├── auth/          # JWT + Firebase
│   │   └── external/      # APIs externas
│   ├── presentation/      # API REST/GraphQL
│   │   ├── controllers/   # Controladores HTTP
│   │   ├── middleware/    # Middlewares
│   │   ├── routes/        # Definición de rutas
│   │   └── validators/    # Validación de entrada
│   └── shared/            # Utilidades compartidas
│       ├── config/        # Configuración
│       ├── logger/        # Sistema de logs
│       └── utils/         # Utilidades generales
├── tests/                 # Tests unitarios e integración
├── prisma/               # Esquemas y migraciones
└── docs/                 # Documentación de API
```

### Mobile (Flutter)

```
mobile/lib/
├── core/                  # Configuración y utilidades
│   ├── config/           # Configuración de la app
│   ├── constants/        # Constantes globales
│   ├── di/              # Inyección de dependencias
│   ├── network/         # Cliente HTTP
│   ├── router/          # Navegación
│   ├── theme/           # Temas y estilos
│   └── utils/           # Utilidades
├── features/             # Características por módulos
│   ├── auth/            # Autenticación
│   │   ├── data/        # Repositorios y fuentes de datos
│   │   ├── domain/      # Entidades y casos de uso
│   │   └── presentation/ # UI y BLoC
│   ├── games/           # Minijuegos
│   ├── rewards/         # Sistema de recompensas
│   ├── profile/         # Perfil de usuario
│   └── leaderboard/     # Clasificaciones
└── shared/              # Componentes compartidos
    ├── widgets/         # Widgets reutilizables
    ├── services/        # Servicios compartidos
    └── models/          # Modelos de datos
```

## 🎮 Desarrollo de Minijuegos

### Crear un Nuevo Minijuego

1. **Backend - Game Engine**
```typescript
// backend/src/application/games/NewGameEngine.ts
export class NewGameEngine implements GameEngine {
  public initialize(config: GameConfig): void {
    // Inicializar juego
  }
  
  public processInput(input: GameInput): void {
    // Procesar entrada del usuario
  }
  
  // Implementar otros métodos requeridos
}
```

2. **Registrar en GameFactory**
```typescript
// backend/src/application/games/GameFactory.ts
private registerEngines(): void {
  this.engines.set(GameType.NEW_GAME, NewGameEngine);
}
```

3. **Frontend - Game Widget**
```dart
// mobile/lib/features/games/presentation/widgets/new_game_widget.dart
class NewGameWidget extends StatefulWidget {
  @override
  _NewGameWidgetState createState() => _NewGameWidgetState();
}
```

### Patrones de Desarrollo

#### Backend - Clean Architecture

```typescript
// Entidad de dominio
export class GameSession {
  constructor(
    public readonly id: string,
    public readonly userId: string,
    // ... otros campos
  ) {}
}

// Repositorio (interfaz)
export interface GameSessionRepository {
  save(session: GameSession): Promise<GameSession>;
  findById(id: string): Promise<GameSession | null>;
}

// Caso de uso
export class StartGameUseCase {
  constructor(
    private gameRepository: GameSessionRepository,
    private tokenService: TokenService
  ) {}
  
  async execute(userId: string, gameType: GameType): Promise<GameSession> {
    // Lógica del caso de uso
  }
}
```

#### Frontend - BLoC Pattern

```dart
// Estado
abstract class GameState extends Equatable {}

class GameInitial extends GameState {}
class GameLoading extends GameState {}
class GamePlaying extends GameState {}
class GameCompleted extends GameState {}

// Eventos
abstract class GameEvent extends Equatable {}

class StartGame extends GameEvent {}
class ProcessInput extends GameEvent {}
class EndGame extends GameEvent {}

// BLoC
class GameBloc extends Bloc<GameEvent, GameState> {
  GameBloc() : super(GameInitial()) {
    on<StartGame>(_onStartGame);
    on<ProcessInput>(_onProcessInput);
    on<EndGame>(_onEndGame);
  }
}
```

## 🧪 Testing

### Backend Testing

```bash
# Tests unitarios
npm test

# Tests con coverage
npm run test:coverage

# Tests de integración
npm run test:integration

# Tests E2E
npm run test:e2e
```

### Mobile Testing

```bash
# Tests unitarios
flutter test

# Tests de integración
flutter test integration_test/

# Tests de widgets
flutter test test/widget_test.dart
```

### Estructura de Tests

```
tests/
├── unit/                 # Tests unitarios
│   ├── services/
│   ├── repositories/
│   └── use-cases/
├── integration/          # Tests de integración
│   ├── api/
│   └── database/
└── e2e/                 # Tests end-to-end
    ├── auth/
    ├── games/
    └── rewards/
```

## 🔧 Herramientas de Desarrollo

### Comandos Útiles

```bash
# Backend
npm run dev              # Desarrollo
npm run build           # Compilar
npm run lint            # Linter
npm run format          # Formatear código
npm run db:migrate      # Migraciones
npm run db:seed         # Poblar BD

# Mobile
flutter run             # Ejecutar app
flutter build apk       # Build Android
flutter build ios       # Build iOS
flutter analyze         # Análisis estático
flutter format .        # Formatear código
```

### Debugging

#### Backend
```bash
# Debug con VS Code
npm run dev:debug

# Logs detallados
DEBUG=* npm run dev
```

#### Mobile
```bash
# Debug en dispositivo
flutter run --debug

# Profile mode
flutter run --profile

# Release mode
flutter run --release
```

### Code Generation

#### Backend
```bash
# Prisma
npx prisma generate
npx prisma db push

# TypeScript
npm run build
```

#### Mobile
```bash
# Build runner
flutter packages pub run build_runner build

# Watch mode
flutter packages pub run build_runner watch
```

## 📊 Monitoreo y Logs

### Logs del Backend

```typescript
import { logger, gameLogger, securityLogger } from '@/shared/logger';

// Log general
logger.info('Usuario autenticado', { userId });

// Log de juego
gameLogger.sessionStart(userId, gameType, sessionId);

// Log de seguridad
securityLogger.loginAttempt(email, success, ip, userAgent);
```

### Métricas y Monitoring

- **Prometheus**: Métricas de aplicación
- **Grafana**: Dashboards y visualización
- **Sentry**: Error tracking
- **New Relic**: Performance monitoring

## 🚀 Despliegue

### Desarrollo
```bash
docker-compose up -d
```

### Staging
```bash
docker-compose -f docker-compose.staging.yml up -d
```

### Producción
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📝 Convenciones de Código

### Commits
```
feat: nueva funcionalidad
fix: corrección de bug
docs: documentación
style: formato de código
refactor: refactorización
test: tests
chore: tareas de mantenimiento
```

### Branches
```
main                    # Producción
develop                # Desarrollo
feature/nueva-feature  # Nuevas características
hotfix/fix-critico    # Correcciones urgentes
release/v1.0.0        # Preparación de release
```

### Naming Conventions

#### Backend (TypeScript)
- **Clases**: PascalCase (`UserService`)
- **Métodos**: camelCase (`getUserById`)
- **Variables**: camelCase (`userId`)
- **Constantes**: UPPER_SNAKE_CASE (`MAX_RETRIES`)

#### Mobile (Dart)
- **Clases**: PascalCase (`GameWidget`)
- **Métodos**: camelCase (`startGame`)
- **Variables**: camelCase (`gameState`)
- **Archivos**: snake_case (`game_widget.dart`)

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-caracteristica`)
3. Commit cambios (`git commit -am 'Añadir nueva característica'`)
4. Push a la rama (`git push origin feature/nueva-caracteristica`)
5. Abrir Pull Request

### Code Review Checklist

- [ ] Tests unitarios incluidos
- [ ] Documentación actualizada
- [ ] Logs apropiados
- [ ] Validación de entrada
- [ ] Manejo de errores
- [ ] Performance considerado
- [ ] Seguridad validada

---

¡Happy Coding! 🎮✨
