version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: code-rush-postgres
    environment:
      POSTGRES_DB: code_rush_db
      POSTGRES_USER: code_rush_user
      POSTGRES_PASSWORD: code_rush_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init:/docker-entrypoint-initdb.d
    networks:
      - code-rush-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: code-rush-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - code-rush-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: code-rush-backend
    environment:
      NODE_ENV: development
      DATABASE_URL: ************************************************************/code_rush_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-at-least-32-characters-long
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-at-least-32-characters-long
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - code-rush-network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: npm run dev

  # Admin Dashboard (Optional)
  admin-dashboard:
    build:
      context: ./admin-dashboard
      dockerfile: Dockerfile
    container_name: code-rush-admin
    environment:
      REACT_APP_API_URL: http://localhost:3000/api
    ports:
      - "3001:3000"
    volumes:
      - ./admin-dashboard:/app
      - /app/node_modules
    networks:
      - code-rush-network
    depends_on:
      - backend
    restart: unless-stopped

  # NGINX Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: code-rush-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - code-rush-network
    depends_on:
      - backend
      - admin-dashboard
    restart: unless-stopped

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: code-rush-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - code-rush-network
    restart: unless-stopped

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: code-rush-grafana
    ports:
      - "3002:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - code-rush-network
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  code-rush-network:
    driver: bridge
