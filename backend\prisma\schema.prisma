// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  username  String   @unique
  password  String?  // Nullable for social login
  provider  String   @default("email") // email, google, facebook
  providerId String?
  isActive  Boolean  @default(true)
  isVerified Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLogin DateTime?

  // Relations
  profile         UserProfile?
  gameSessions    GameSession[]
  tokenTransactions TokenTransaction[]
  dailyMissions   DailyMission[]
  achievements    UserAchievement[]
  fraudReports    FraudReport[]
  adViews         AdView[]
  surveyResponses SurveyResponse[]

  @@map("users")
}

model UserProfile {
  userId       String  @id
  displayName  String
  avatarUrl    String?
  level        Int     @default(1)
  experience   Int     @default(0)
  totalIbTokens Int    @default(0)
  gamesPlayed  Int     @default(0)
  totalScore   Int     @default(0)
  preferences  Json?   // Game preferences, notifications, etc.
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

model GameSession {
  id              String   @id @default(uuid())
  userId          String
  gameType        String   // matching, sequence, timing, puzzle
  difficulty      Int      @default(1)
  score           Int
  maxScore        Int?     // Perfect score for this game
  durationSeconds Int
  ibTokensEarned  Int      @default(0)
  isCompleted     Boolean  @default(false)
  isPerfect       Boolean  @default(false)
  metadata        Json?    // Game-specific data
  startedAt       DateTime @default(now())
  completedAt     DateTime?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("game_sessions")
  @@index([userId, gameType])
  @@index([completedAt])
}

model TokenTransaction {
  id          String   @id @default(uuid())
  userId      String
  type        String   // earned, spent, withdrawn, bonus
  amount      Int
  source      String   // game, ad, survey, bonus, purchase
  referenceId String?  // Reference to game session, ad view, etc.
  description String?
  metadata    Json?
  createdAt   DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("token_transactions")
  @@index([userId, type])
  @@index([createdAt])
}

model DailyMission {
  id              String    @id @default(uuid())
  userId          String
  missionType     String    // play_games, earn_tokens, complete_perfect, etc.
  targetValue     Int
  currentProgress Int       @default(0)
  rewardTokens    Int
  isCompleted     Boolean   @default(false)
  expiresAt       DateTime
  completedAt     DateTime?
  createdAt       DateTime  @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("daily_missions")
  @@index([userId, expiresAt])
}

model Achievement {
  id          String @id @default(uuid())
  name        String @unique
  description String
  category    String // gameplay, social, progression, special
  iconUrl     String?
  rewardTokens Int   @default(0)
  criteria    Json   // Conditions to unlock
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())

  // Relations
  userAchievements UserAchievement[]

  @@map("achievements")
}

model UserAchievement {
  id            String   @id @default(uuid())
  userId        String
  achievementId String
  unlockedAt    DateTime @default(now())
  progress      Json?    // Progress towards achievement

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

model AdView {
  id          String   @id @default(uuid())
  userId      String
  adType      String   // interstitial, rewarded, banner
  adProvider  String   // admob, unity, etc.
  tokensEarned Int     @default(0)
  isVerified  Boolean  @default(false)
  metadata    Json?    // Ad-specific data
  viewedAt    DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ad_views")
  @@index([userId, viewedAt])
}

model SurveyResponse {
  id           String   @id @default(uuid())
  userId       String
  surveyId     String
  provider     String   // survey provider
  tokensEarned Int      @default(0)
  isVerified   Boolean  @default(false)
  metadata     Json?
  completedAt  DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("survey_responses")
  @@index([userId, completedAt])
}

model FraudReport {
  id          String   @id @default(uuid())
  userId      String
  reportType  String   // suspicious_pattern, impossible_score, bot_behavior
  severity    String   // low, medium, high, critical
  description String
  evidence    Json     // Data supporting the report
  status      String   @default("pending") // pending, investigating, resolved, false_positive
  actionTaken String?  // warning, suspension, ban, none
  createdAt   DateTime @default(now())
  resolvedAt  DateTime?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("fraud_reports")
  @@index([userId, status])
  @@index([createdAt])
}

model GameConfig {
  id          String   @id @default(uuid())
  gameType    String   @unique
  config      Json     // Game-specific configuration
  isActive    Boolean  @default(true)
  version     String   @default("1.0.0")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("game_configs")
}

model SystemConfig {
  key       String   @id
  value     Json
  category  String   // rewards, security, features, etc.
  isActive  Boolean  @default(true)
  updatedAt DateTime @updatedAt

  @@map("system_configs")
}

model Leaderboard {
  id        String   @id @default(uuid())
  gameType  String
  period    String   // daily, weekly, monthly, all_time
  userId    String
  score     Int
  rank      Int
  metadata  Json?
  createdAt DateTime @default(now())

  @@unique([gameType, period, userId])
  @@map("leaderboards")
  @@index([gameType, period, rank])
}
