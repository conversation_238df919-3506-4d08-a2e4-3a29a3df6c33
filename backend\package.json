{"name": "code-rush-backend", "version": "1.0.0", "description": "Backend API for Code Rush mobile game", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset", "docker:build": "docker build -t code-rush-backend .", "docker:run": "docker run -p 3000:3000 code-rush-backend"}, "keywords": ["game", "mobile", "api", "typescript", "nodejs", "express"], "author": "Code Rush Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "zod": "^3.22.4", "redis": "^4.6.10", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^11.11.1", "stripe": "^14.9.0", "node-cron": "^3.0.3", "uuid": "^9.0.1", "moment": "^2.29.4", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.10.4", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^9.0.7", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prisma": "^5.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}