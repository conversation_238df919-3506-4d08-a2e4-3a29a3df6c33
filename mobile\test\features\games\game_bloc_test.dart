import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:code_rush/features/games/domain/entities/game_session.dart';
import 'package:code_rush/features/games/domain/repositories/game_repository.dart';
import 'package:code_rush/features/games/presentation/bloc/game_bloc.dart';

class MockGameRepository extends Mock implements GameRepository {}

void main() {
  group('GameBloc', () {
    late GameRepository mockGameRepository;
    late GameBloc gameBloc;

    setUp(() {
      mockGameRepository = MockGameRepository();
      gameBloc = GameBloc(gameRepository: mockGameRepository);
    });

    tearDown(() {
      gameBloc.close();
    });

    test('initial state is GameInitial', () {
      expect(gameBloc.state, equals(const GameInitial()));
    });

    group('StartGame', () {
      const gameType = 'matching';
      const difficulty = 3;
      
      final gameSession = GameSession(
        id: 'session-123',
        userId: 'user-123',
        gameType: gameType,
        difficulty: difficulty,
        score: 0,
        isCompleted: false,
        startedAt: DateTime.now(),
      );

      blocTest<GameBloc, GameState>(
        'emits [GameLoading, GamePlaying] when game starts successfully',
        build: () {
          when(() => mockGameRepository.startGame(gameType, difficulty))
              .thenAnswer((_) async => gameSession);
          return gameBloc;
        },
        act: (bloc) => bloc.add(const StartGame(
          gameType: gameType,
          difficulty: difficulty,
        )),
        expect: () => [
          const GameLoading(),
          GamePlaying(session: gameSession),
        ],
        verify: (_) {
          verify(() => mockGameRepository.startGame(gameType, difficulty))
              .called(1);
        },
      );

      blocTest<GameBloc, GameState>(
        'emits [GameLoading, GameError] when game start fails',
        build: () {
          when(() => mockGameRepository.startGame(gameType, difficulty))
              .thenThrow(Exception('Failed to start game'));
          return gameBloc;
        },
        act: (bloc) => bloc.add(const StartGame(
          gameType: gameType,
          difficulty: difficulty,
        )),
        expect: () => [
          const GameLoading(),
          const GameError(message: 'Failed to start game'),
        ],
      );
    });

    group('ProcessInput', () {
      final gameSession = GameSession(
        id: 'session-123',
        userId: 'user-123',
        gameType: 'matching',
        difficulty: 3,
        score: 0,
        isCompleted: false,
        startedAt: DateTime.now(),
      );

      const gameInput = {
        'type': 'select_item',
        'data': {'itemId': 'item-1'},
      };

      blocTest<GameBloc, GameState>(
        'processes input and updates game state',
        build: () {
          when(() => mockGameRepository.processInput('session-123', gameInput))
              .thenAnswer((_) async => gameSession.copyWith(score: 100));
          return gameBloc;
        },
        seed: () => GamePlaying(session: gameSession),
        act: (bloc) => bloc.add(const ProcessInput(input: gameInput)),
        expect: () => [
          GamePlaying(session: gameSession.copyWith(score: 100)),
        ],
        verify: (_) {
          verify(() => mockGameRepository.processInput('session-123', gameInput))
              .called(1);
        },
      );

      blocTest<GameBloc, GameState>(
        'emits GameCompleted when game is finished',
        build: () {
          final completedSession = gameSession.copyWith(
            score: 1000,
            isCompleted: true,
            completedAt: DateTime.now(),
          );
          
          when(() => mockGameRepository.processInput('session-123', gameInput))
              .thenAnswer((_) async => completedSession);
          return gameBloc;
        },
        seed: () => GamePlaying(session: gameSession),
        act: (bloc) => bloc.add(const ProcessInput(input: gameInput)),
        expect: () => [
          GameCompleted(session: gameSession.copyWith(
            score: 1000,
            isCompleted: true,
            completedAt: DateTime.now(),
          )),
        ],
      );
    });

    group('EndGame', () => {
      final gameSession = GameSession(
        id: 'session-123',
        userId: 'user-123',
        gameType: 'matching',
        difficulty: 3,
        score: 500,
        isCompleted: false,
        startedAt: DateTime.now(),
      );

      final gameResult = GameResult(
        sessionId: 'session-123',
        score: 500,
        maxScore: 1000,
        isCompleted: false,
        isPerfect: false,
        durationSeconds: 120,
        tokensEarned: 25,
      );

      blocTest<GameBloc, GameState>(
        'emits [GameLoading, GameCompleted] when game ends successfully',
        build: () {
          when(() => mockGameRepository.endGame('session-123'))
              .thenAnswer((_) async => gameResult);
          return gameBloc;
        },
        seed: () => GamePlaying(session: gameSession),
        act: (bloc) => bloc.add(const EndGame()),
        expect: () => [
          const GameLoading(),
          GameCompleted(
            session: gameSession.copyWith(isCompleted: true),
            result: gameResult,
          ),
        ],
        verify: (_) {
          verify(() => mockGameRepository.endGame('session-123')).called(1);
        },
      );
    });

    group('PauseGame', () => {
      final gameSession = GameSession(
        id: 'session-123',
        userId: 'user-123',
        gameType: 'matching',
        difficulty: 3,
        score: 200,
        isCompleted: false,
        startedAt: DateTime.now(),
      );

      blocTest<GameBloc, GameState>(
        'pauses game successfully',
        build: () {
          when(() => mockGameRepository.pauseGame('session-123'))
              .thenAnswer((_) async => gameSession);
          return gameBloc;
        },
        seed: () => GamePlaying(session: gameSession),
        act: (bloc) => bloc.add(const PauseGame()),
        expect: () => [
          GamePaused(session: gameSession),
        ],
        verify: (_) {
          verify(() => mockGameRepository.pauseGame('session-123')).called(1);
        },
      );
    });

    group('ResumeGame', () => {
      final gameSession = GameSession(
        id: 'session-123',
        userId: 'user-123',
        gameType: 'matching',
        difficulty: 3,
        score: 200,
        isCompleted: false,
        startedAt: DateTime.now(),
      );

      blocTest<GameBloc, GameState>(
        'resumes game successfully',
        build: () {
          when(() => mockGameRepository.resumeGame('session-123'))
              .thenAnswer((_) async => gameSession);
          return gameBloc;
        },
        seed: () => GamePaused(session: gameSession),
        act: (bloc) => bloc.add(const ResumeGame()),
        expect: () => [
          GamePlaying(session: gameSession),
        ],
        verify: (_) {
          verify(() => mockGameRepository.resumeGame('session-123')).called(1);
        },
      );
    });

    group('ResetGame', () => {
      final gameSession = GameSession(
        id: 'session-123',
        userId: 'user-123',
        gameType: 'matching',
        difficulty: 3,
        score: 500,
        isCompleted: true,
        startedAt: DateTime.now(),
      );

      blocTest<GameBloc, GameState>(
        'resets game to initial state',
        build: () => gameBloc,
        seed: () => GameCompleted(session: gameSession),
        act: (bloc) => bloc.add(const ResetGame()),
        expect: () => [
          const GameInitial(),
        ],
      );
    });

    group('edge cases', () => {
      blocTest<GameBloc, GameState>(
        'ignores ProcessInput when not playing',
        build: () => gameBloc,
        seed: () => const GameInitial(),
        act: (bloc) => bloc.add(const ProcessInput(input: {'type': 'test'})),
        expect: () => [],
      );

      blocTest<GameBloc, GameState>(
        'ignores PauseGame when not playing',
        build: () => gameBloc,
        seed: () => const GameInitial(),
        act: (bloc) => bloc.add(const PauseGame()),
        expect: () => [],
      );

      blocTest<GameBloc, GameState>(
        'ignores ResumeGame when not paused',
        build: () => gameBloc,
        seed: () => const GameInitial(),
        act: (bloc) => bloc.add(const ResumeGame()),
        expect: () => [],
      );

      blocTest<GameBloc, GameState>(
        'ignores EndGame when not playing',
        build: () => gameBloc,
        seed: () => const GameInitial(),
        act: (bloc) => bloc.add(const EndGame()),
        expect: () => [],
      );
    });
  });
}
