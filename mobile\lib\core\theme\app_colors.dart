import 'package:flutter/material.dart';

/// Cybertech color palette for Code Rush
class AppColors {
  // Primary Neon Colors
  static const Color neonBlue = Color(0xFF00D4FF);
  static const Color neonPurple = Color(0xFF8B5CF6);
  static const Color neonGreen = Color(0xFF00FF88);
  static const Color neonPink = Color(0xFFFF0080);
  static const Color neonYellow = Color(0xFFFFD700);
  static const Color neonRed = Color(0xFFFF3366);
  static const Color neonOrange = Color(0xFFFF6B35);

  // Matrix/Cyber Colors
  static const Color matrixGreen = Color(0xFF00FF41);
  static const Color cyberBlue = Color(0xFF0099FF);
  static const Color digitalPurple = Color(0xFF9D4EDD);
  static const Color hologramBlue = Color(0xFF4CC9F0);

  // Background Colors
  static const Color darkBackground = Color(0xFF0A0A0F);
  static const Color darkSurface = Color(0xFF1A1A2E);
  static const Color lightBackground = Color(0xFFF8F9FA);
  static const Color lightSurface = Color(0xFFFFFFFF);

  // Text Colors
  static const Color lightText = Color(0xFFFFFFFF);
  static const Color darkText = Color(0xFF1A1A2E);
  static const Color greyText = Color(0xFF8E8E93);
  static const Color mutedText = Color(0xFF6C757D);

  // Gradient Colors
  static const List<Color> neonGradient = [
    neonBlue,
    neonPurple,
  ];

  static const List<Color> matrixGradient = [
    matrixGreen,
    cyberBlue,
  ];

  static const List<Color> fireGradient = [
    neonOrange,
    neonRed,
  ];

  static const List<Color> cosmicGradient = [
    digitalPurple,
    neonPink,
  ];

  // Game-specific Colors
  static const Color successGreen = Color(0xFF28A745);
  static const Color warningYellow = Color(0xFFFFC107);
  static const Color errorRed = Color(0xFFDC3545);
  static const Color infoBlue = Color(0xFF17A2B8);

  // Token/Currency Colors
  static const Color tokenGold = Color(0xFFFFD700);
  static const Color tokenSilver = Color(0xFFC0C0C0);
  static const Color tokenBronze = Color(0xFFCD7F32);

  // Game Difficulty Colors
  static const Color easyGreen = Color(0xFF4CAF50);
  static const Color mediumOrange = Color(0xFFFF9800);
  static const Color hardRed = Color(0xFFF44336);
  static const Color expertPurple = Color(0xFF9C27B0);
  static const Color masterGold = Color(0xFFFFD700);

  // UI State Colors
  static const Color disabledGrey = Color(0xFF9E9E9E);
  static const Color selectedBlue = Color(0xFF2196F3);
  static const Color hoverGrey = Color(0xFFE0E0E0);
  static const Color focusBlue = Color(0xFF1976D2);

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
  static const Color glowBlue = Color(0x4D00D4FF);
  static const Color glowPurple = Color(0x4D8B5CF6);

  // Transparent Colors
  static const Color transparent = Colors.transparent;
  static const Color semiTransparent = Color(0x80000000);

  // Game Type Colors
  static const Color matchingGame = neonBlue;
  static const Color sequenceGame = neonPurple;
  static const Color timingGame = neonGreen;
  static const Color puzzleGame = neonPink;

  // Achievement Colors
  static const Color bronzeAchievement = Color(0xFFCD7F32);
  static const Color silverAchievement = Color(0xFFC0C0C0);
  static const Color goldAchievement = Color(0xFFFFD700);
  static const Color platinumAchievement = Color(0xFFE5E4E2);
  static const Color diamondAchievement = Color(0xFFB9F2FF);

  // Leaderboard Colors
  static const Color firstPlace = Color(0xFFFFD700);
  static const Color secondPlace = Color(0xFFC0C0C0);
  static const Color thirdPlace = Color(0xFFCD7F32);
  static const Color topTen = Color(0xFF4CAF50);
  static const Color topHundred = Color(0xFF2196F3);

  // Ad/Monetization Colors
  static const Color rewardedAdGreen = Color(0xFF4CAF50);
  static const Color interstitialAdBlue = Color(0xFF2196F3);
  static const Color bannerAdGrey = Color(0xFF9E9E9E);

  // Social Colors
  static const Color facebookBlue = Color(0xFF1877F2);
  static const Color googleRed = Color(0xFFDB4437);
  static const Color twitterBlue = Color(0xFF1DA1F2);
  static const Color discordPurple = Color(0xFF7289DA);

  // Utility Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static LinearGradient createGradient(
    List<Color> colors, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: colors,
      begin: begin,
      end: end,
    );
  }

  static RadialGradient createRadialGradient(
    List<Color> colors, {
    AlignmentGeometry center = Alignment.center,
    double radius = 0.5,
  }) {
    return RadialGradient(
      colors: colors,
      center: center,
      radius: radius,
    );
  }

  static BoxShadow createGlow(
    Color color, {
    double blurRadius = 10.0,
    double spreadRadius = 2.0,
    Offset offset = Offset.zero,
  }) {
    return BoxShadow(
      color: color.withOpacity(0.5),
      blurRadius: blurRadius,
      spreadRadius: spreadRadius,
      offset: offset,
    );
  }

  static BoxShadow createNeonGlow(Color color) {
    return BoxShadow(
      color: color.withOpacity(0.6),
      blurRadius: 20.0,
      spreadRadius: 5.0,
      offset: Offset.zero,
    );
  }

  // Color schemes for different game modes
  static const Map<String, Color> gameTypeColors = {
    'matching': matchingGame,
    'sequence': sequenceGame,
    'timing': timingGame,
    'puzzle': puzzleGame,
  };

  static const Map<int, Color> difficultyColors = {
    1: easyGreen,
    2: easyGreen,
    3: mediumOrange,
    4: mediumOrange,
    5: hardRed,
    6: hardRed,
    7: expertPurple,
    8: expertPurple,
    9: masterGold,
    10: masterGold,
  };

  static const Map<String, Color> achievementTierColors = {
    'bronze': bronzeAchievement,
    'silver': silverAchievement,
    'gold': goldAchievement,
    'platinum': platinumAchievement,
    'diamond': diamondAchievement,
  };
}
